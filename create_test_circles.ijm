// ImageJ Macro to Create Test Circles with Linearly Growing Areas
// Creates circles with areas from 300 to 100,000 pixels for robustness testing

// =============================================================================
// TEST PARAMETERS
// =============================================================================

// Circle area parameters
minAreaPixels = 300;        // Smallest circle area
maxAreaPixels = 100000;     // Largest circle area
numCircles = 10;            // Number of circles to create

// Image settings
imageWidth = 1200;          // Image width
imageHeight = 800;          // Image height
backgroundColor = 0;        // Black background
circleColor = 255;         // White circles

// Layout settings
marginX = 100;             // Margin from edges
marginY = 100;             // Margin from edges
spacing = 20;              // Minimum spacing between circles

print("\\Clear");
print("=== CREATING TEST CIRCLES FOR ROBUSTNESS VALIDATION ===");
print("Circle areas: " + minAreaPixels + " to " + maxAreaPixels + " pixels");
print("Number of circles: " + numCircles);
print("");

// =============================================================================
// CALCULATE CIRCLE PARAMETERS
// =============================================================================

// Calculate areas with linear progression
areas = newArray(numCircles);
radii = newArray(numCircles);
diameters = newArray(numCircles);

areaStep = (maxAreaPixels - minAreaPixels) / (numCircles - 1);

print("--- CIRCLE SPECIFICATIONS ---");
for (i = 0; i < numCircles; i++) {
    // Linear area progression
    areas[i] = minAreaPixels + i * areaStep;
    
    // Calculate radius from area: A = π * r²  =>  r = sqrt(A/π)
    radii[i] = sqrt(areas[i] / PI);
    diameters[i] = 2 * radii[i];
    
    print("Circle " + (i+1) + ": Area = " + d2s(areas[i], 0) + " pixels, Radius = " + d2s(radii[i], 1) + " pixels, Diameter = " + d2s(diameters[i], 1) + " pixels");
}
print("");

// =============================================================================
// CALCULATE LAYOUT POSITIONS
// =============================================================================

// Calculate optimal layout (arrange in rows)
maxRadius = radii[numCircles-1];
circlesPerRow = floor((imageWidth - 2 * marginX + spacing) / (2 * maxRadius + spacing));
numRows = ceil(numCircles / circlesPerRow);

print("Layout: " + circlesPerRow + " circles per row, " + numRows + " rows");

// Calculate positions
xPositions = newArray(numCircles);
yPositions = newArray(numCircles);

for (i = 0; i < numCircles; i++) {
    row = floor(i / circlesPerRow);
    col = i % circlesPerRow;
    
    // Calculate x position (center circles in each row)
    circlesInThisRow = minOf(circlesPerRow, numCircles - row * circlesPerRow);
    rowWidth = circlesInThisRow * (2 * maxRadius + spacing) - spacing;
    startX = (imageWidth - rowWidth) / 2 + maxRadius;
    xPositions[i] = startX + col * (2 * maxRadius + spacing);
    
    // Calculate y position
    rowHeight = 2 * maxRadius + spacing;
    totalHeight = numRows * rowHeight - spacing;
    startY = (imageHeight - totalHeight) / 2 + maxRadius;
    yPositions[i] = startY + row * rowHeight;
}

// =============================================================================
// CREATE TEST IMAGE
// =============================================================================

newImage("Test_Circles_Linear_Area", "8-bit black", imageWidth, imageHeight, 1);
setForegroundColor(circleColor, circleColor, circleColor);

print("--- CREATING CIRCLES ---");

for (i = 0; i < numCircles; i++) {
    x = xPositions[i];
    y = yPositions[i];
    r = radii[i];
    
    // Draw filled circle
    makeOval(x - r, y - r, 2 * r, 2 * r);
    fill();
    
    // Add text label above circle
    setColor(circleColor);
    setFont("SansSerif", 12, "bold");
    labelText = "" + (i+1);
    drawString(labelText, x - 5, y - r - 10);
    
    print("Created circle " + (i+1) + " at (" + d2s(x, 0) + ", " + d2s(y, 0) + ")");
}

run("Select None");

// =============================================================================
// THEORETICAL ROBUSTNESS CALCULATIONS
// =============================================================================

print("");
print("--- THEORETICAL ROBUSTNESS VALUES ---");
print("For perfect circles, robustness O1 = 2*o1/sqrt(A)");
print("Theoretical o1 for circle ≈ radius (number of erosions to disappear)");
print("Therefore, theoretical O1 ≈ 2*r/sqrt(A) = 2*sqrt(A/π)/sqrt(A) = 2/sqrt(π) ≈ 1.128");
print("");

for (i = 0; i < numCircles; i++) {
    theoreticalO1 = 2.0 / sqrt(PI);  // Theoretical value for perfect circle
    expectedErosions = radii[i];     // Approximate number of erosions
    
    print("Circle " + (i+1) + ": Expected ~" + d2s(expectedErosions, 0) + " erosions, Theoretical O1 ≈ " + d2s(theoreticalO1, 3));
}

// =============================================================================
// VALIDATION INFORMATION
// =============================================================================

print("");
print("=== VALIDATION INSTRUCTIONS ===");
print("1. Run particle_robustness.ijm on this test image");
print("2. Check that erosion counts approximately equal the radius values above");
print("3. Verify that all circles give similar O1 values around 1.128");
print("4. Larger circles should take more erosions but give same O1");
print("5. If O1 values vary significantly, check the erosion detection method");
print("");

print("=== EXPECTED RESULTS ===");
print("- Erosion counts should range from ~" + d2s(radii[0], 0) + " to ~" + d2s(radii[numCircles-1], 0));
print("- All O1 values should be close to 1.128 (theoretical for circles)");
print("- Variation in O1 should be minimal (<5%) for perfect circles");
print("");

// =============================================================================
// CREATE SUMMARY TABLE
// =============================================================================

// Create summary table with expected values
if (isOpen("Circle_Test_Summary")) {
    selectWindow("Circle_Test_Summary");
    run("Close");
}

run("Table...", "name=Circle_Test_Summary width=500 height=300");
print("[Circle_Test_Summary]", "\\Headings:Circle\tArea_pixels\tRadius_pixels\tExpected_Erosions\tTheoretical_O1");

for (i = 0; i < numCircles; i++) {
    theoreticalO1 = 2.0 / sqrt(PI);
    print("[Circle_Test_Summary]", "" + (i+1) + "\t" + d2s(areas[i], 0) + "\t" + d2s(radii[i], 1) + "\t" + d2s(radii[i], 0) + "\t" + d2s(theoreticalO1, 4));
}

print("");
print("Test circles created successfully!");
print("Image: Test_Circles_Linear_Area");
print("Summary table: Circle_Test_Summary");

// =============================================================================
// ADDITIONAL TEST INFORMATION
// =============================================================================

print("");
print("=== ROBUSTNESS TESTING NOTES ===");
print("- Circle robustness should be independent of size");
print("- All circles should give O1 ≈ 1.128 regardless of area");
print("- This tests the scale-invariance of the robustness measure");
print("- Deviations from 1.128 indicate issues with the calculation method");
print("");
print("=== CALIBRATION TESTING ===");
print("If using calibration (214 pixels = 50 µm):");
micronsPerPixel = 50.0 / 214.0;
print("- Smallest circle: " + d2s(areas[0] * micronsPerPixel * micronsPerPixel, 1) + " µm²");
print("- Largest circle: " + d2s(areas[numCircles-1] * micronsPerPixel * micronsPerPixel, 0) + " µm²");
print("- Robustness O1 should still be ~1.128 for all circles");
print("- This tests calibration independence of robustness");
