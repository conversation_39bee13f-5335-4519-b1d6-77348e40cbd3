// Combined ImageJ Macro for Comprehensive Particle Analysis
// Calculates Robustness (O1), Largest Concavity Index (O2), and Average Concavity
// Optimized for speed and integration with larger analysis workflows

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";
run("Set Scale...", "distance=1 known=1 unit=pixels");

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
maxErosions = 200;               // Maximum number of erosions to try
angleStepDegrees = 5;           // Angle step for Feret diameter calculation
useSubpixelRefinement = true;   // Enable subpixel boundary refinement

// Results table names
robustnessTableName = "Particle_Robustness_Results";
largestConcavityTableName = "Largest_Concavity_Results";
averageConcavityTableName = "Average_Concavity_Results";
combinedTableName = "Combined_Particle_Analysis";

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
originalID = getImageID();

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;

// Control verbose output (skip when Shift is held for batch processing)
verboseMode = !isKeyDown("shift");

if (verboseMode) {
    print("\\Clear");
    print("=== COMPREHENSIVE PARTICLE ANALYSIS ===");
    print("Image: " + originalTitle);
    print("Calculating Robustness (O1), Largest Concavity Index (O2), and Average Concavity");
    print("");
}

// =============================================================================
// IMAGE PREPROCESSING
// =============================================================================

// Create working copy
selectImage(originalID);
run("Duplicate...", "title=Working");
workingID = getImageID();

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// Analyze particles to get basic measurements and ROIs
run("Set Measurements...", "area perimeter centroid redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude clear add");

nParticles = nResults;
if (nParticles == 0) {
    exit("No particles found above size threshold.");
}

if (verboseMode) {
    print("Found " + nParticles + " particles for analysis");
}

// Pre-calculate trigonometric values for average concavity calculation
numAngles = 180 / angleStepDegrees;
cosValues = newArray(numAngles);
sinValues = newArray(numAngles);

for (i = 0; i < numAngles; i++) {
    angleRad = i * angleStepDegrees * PI / 180;
    cosValues[i] = cos(angleRad);
    sinValues[i] = sin(angleRad);
}

// Arrays to store results
robustnessValues = newArray(nParticles);
robustnessErosions = newArray(nParticles);
largestConcavityValues = newArray(nParticles);
largestConcavityErosions = newArray(nParticles);
averageConcavityValues = newArray(nParticles);
particleAreas = newArray(nParticles);
particlePerimeters = newArray(nParticles);

// Enable batch mode for speed
setBatchMode(true);

// =============================================================================
// MAIN ANALYSIS LOOP
// =============================================================================

if (verboseMode) {
    print("Analyzing particles...");
}

for (p = 0; p < nParticles; p++) {
    showProgress(p, nParticles);

    // Get particle measurements from results table (in pixels)
    areaPixels = getResult("Area", p);
    perimeterPixels = getResult("Perim.", p);
    areaMicrons = areaPixels * micronsPerPixel * micronsPerPixel;
    perimeterMicrons = perimeterPixels * micronsPerPixel;
    particleAreas[p] = areaMicrons;
    particlePerimeters[p] = perimeterMicrons;
    
    // =============================================================================
    // ROBUSTNESS ANALYSIS (O1)
    // =============================================================================
    
    // Create minimal working image for robustness analysis
    roiManager("select", p);
    Roi.getBounds(x, y, width, height);
    
    newImage("RobustnessWork", "8-bit black", width + 20, height + 20, 1);
    robustnessWorkID = getImageID();
    
    // Copy particle to smaller image
    selectImage(workingID);
    roiManager("select", p);
    run("Copy");
    selectImage(robustnessWorkID);
    makeRectangle(10, 10, width, height);
    run("Paste");
    run("Select None");
    
    // Count erosions for robustness
    erosionCount = 0;
    particleExists = true;
    
    getHistogram(values, counts, 256);
    if (counts[255] == 0) particleExists = false;
    
    while (particleExists && erosionCount < maxErosions) {
        run("Erode");
        erosionCount++;
        getHistogram(values, counts, 256);
        if (counts[255] == 0) particleExists = false;
    }
    
    robustnessErosions[p] = erosionCount;
    if (areaPixels > 0) {
        robustnessValues[p] = (2.0 * erosionCount) / sqrt(areaPixels);
    } else {
        robustnessValues[p] = 0;
    }
    
    selectImage(robustnessWorkID);
    close();

    // =============================================================================
    // AVERAGE CONCAVITY ANALYSIS
    // =============================================================================

    // Calculate average concavity using Feret diameter method
    roiManager("select", p);
    Roi.getCoordinates(xPixels, yPixels);
    nPoints = xPixels.length;

    if (nPoints >= 3) {
        // Apply subpixel refinement if enabled
        if (useSubpixelRefinement) {
            refineParticleBoundary(xPixels, yPixels);
        }

        // Convert coordinates to calibrated units (micrometers)
        xMicrons = newArray(nPoints);
        yMicrons = newArray(nPoints);
        for (i = 0; i < nPoints; i++) {
            xMicrons[i] = xPixels[i] * micronsPerPixel;
            yMicrons[i] = yPixels[i] * micronsPerPixel;
        }

        // Calculate integrated Feret diameter
        feretSum = 0;

        for (angleIdx = 0; angleIdx < numAngles; angleIdx++) {
            cosVal = cosValues[angleIdx];
            sinVal = sinValues[angleIdx];

            // Project all points onto the current direction
            minProjection = 1e10;
            maxProjection = -1e10;

            for (i = 0; i < nPoints; i++) {
                projection = xMicrons[i] * cosVal + yMicrons[i] * sinVal;
                if (projection < minProjection) minProjection = projection;
                if (projection > maxProjection) maxProjection = projection;
            }

            feretDiameter = maxProjection - minProjection;
            feretSum += feretDiameter;
        }

        // Calculate average Feret diameter and concavity
        avgFeretDiameter = feretSum / numAngles;
        perimeterEquivDiameterMicrons = perimeterMicrons / PI;
        averageConcavity = avgFeretDiameter / perimeterEquivDiameterMicrons;
        averageConcavityValues[p] = averageConcavity;
    } else {
        averageConcavityValues[p] = 0;
    }

    // =============================================================================
    // LARGEST CONCAVITY INDEX ANALYSIS (O2)
    // =============================================================================
    
    // Create working images for largest concavity analysis
    roiManager("select", p);
    Roi.getCoordinates(xCoords, yCoords);
    nPoints = xCoords.length;
    
    if (nPoints >= 3) {
        // Create minimal working image for this particle
        newImage("ConcavityWork", "8-bit black", width + 20, height + 20, 1);
        concavityWorkID = getImageID();
        
        // Translate coordinates to fit in smaller image
        for (i = 0; i < nPoints; i++) {
            xCoords[i] = xCoords[i] - x + 10;
            yCoords[i] = yCoords[i] - y + 10;
        }
        makeSelection("polygon", xCoords, yCoords);
        run("Fill", "slice");
        
        // Create convex hull version
        run("Duplicate...", "title=ConvexHull");
        convexHullID = getImageID();
        run("Convex Hull");
        run("Fill", "slice");
        
        // Calculate concave regions (difference)
        imageCalculator("Subtract create", convexHullID, concavityWorkID);
        concaveRegionsID = getImageID();
        
        // Clean up intermediate images
        selectImage(convexHullID);
        close();
        selectImage(concavityWorkID);
        close();
        
        // Count erosions on concave regions
        selectImage(concaveRegionsID);
        erosionCount = 0;
        particleExists = true;
        
        getHistogram(values, counts, 256);
        if (counts[255] == 0) particleExists = false;
        
        while (particleExists && erosionCount < maxErosions) {
            run("Erode");
            erosionCount++;
            getHistogram(values, counts, 256);
            if (counts[255] == 0) particleExists = false;
        }
        
        largestConcavityErosions[p] = erosionCount;
        if (areaPixels > 0) {
            largestConcavityValues[p] = (2.0 * erosionCount) / sqrt(areaPixels);
        } else {
            largestConcavityValues[p] = 0;
        }

        selectImage(concaveRegionsID);
        close();
    } else {
        largestConcavityErosions[p] = 0;
        largestConcavityValues[p] = 0;
    }
}

// Re-enable screen updates
setBatchMode(false);

// Clean up
selectImage(workingID);
close();
roiManager("reset");

// =============================================================================
// RESULTS OUTPUT
// =============================================================================

// Create individual results tables
if (isOpen(robustnessTableName)) {
    selectWindow(robustnessTableName);
    run("Close");
}
if (isOpen(concavityTableName)) {
    selectWindow(concavityTableName);
    run("Close");
}
if (isOpen(combinedTableName)) {
    selectWindow(combinedTableName);
    run("Close");
}

// Create particle numbers starting from 1 to match ROI numbers
particleNumbers = newArray(nParticles);
for (i = 0; i < nParticles; i++) {
    particleNumbers[i] = i + 1;  // Add 1 to match ROI numbering
}

// Create robustness results table
Table.create(robustnessTableName);
selectWindow(robustnessTableName);
Table.setColumn("Particle", particleNumbers);
Table.setColumn("Area_" + unit + "²", particleAreas);
Table.setColumn("Erosions_o1", robustnessErosions);
Table.setColumn("Robustness_O1", robustnessValues);
Table.update;

// Create concavity results table
Table.create(concavityTableName);
selectWindow(concavityTableName);
Table.setColumn("Particle", particleNumbers);
Table.setColumn("Area_" + unit + "²", particleAreas);
Table.setColumn("Erosions_o2", concavityErosions);
Table.setColumn("Concavity_Index_O2", concavityValues);
Table.update;

// Create combined results table
Table.create(combinedTableName);
selectWindow(combinedTableName);
Table.setColumn("Particle", particleNumbers);
Table.setColumn("Area_" + unit + "²", particleAreas);
Table.setColumn("Erosions_o1", robustnessErosions);
Table.setColumn("Robustness_O1", robustnessValues);
Table.setColumn("Erosions_o2", concavityErosions);
Table.setColumn("Concavity_Index_O2", concavityValues);
Table.update;

// Calculate and display summary statistics
robustnessSum = 0;
concavitySum = 0;
validCount = 0;

for (i = 0; i < nParticles; i++) {
    robustnessSum += robustnessValues[i];
    concavitySum += concavityValues[i];
    validCount++;
}

avgRobustness = robustnessSum / validCount;
avgConcavity = concavitySum / validCount;

if (verboseMode) {
    print("");
    print("=== ANALYSIS COMPLETE ===");
    print("Particles analyzed: " + validCount);
    print("Average Robustness (O1): " + d2s(avgRobustness, 4));
    print("Average Concavity Index (O2): " + d2s(avgConcavity, 4));
    print("");
    print("Results stored in tables:");
    print("- " + robustnessTableName);
    print("- " + concavityTableName);
    print("- " + combinedTableName);
}
