print("\\Clear");
print("Macro loaded successfully.");
IJ.log("DEBUG: Running macro");
args = getArgument();
argArray = split(args, ",");
pixels = argArray[0];
microns = argArray[1];
print("Arg1: " + pixels);
print("Arg2: " + microns);

/*
 *print("DEBUG: Running macro");
args = getArgument();
argArray = split(args, ",");
pixels = argArray[0];
microns = argArray[1];
print("arg[0]: "+pixels);
print("arg[1]: "+microns);
 */