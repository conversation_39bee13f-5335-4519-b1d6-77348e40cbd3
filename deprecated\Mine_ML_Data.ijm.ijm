// ML_Mining - C000T4f16M [Mine Data required for ML models] [M]
macro "ML_Mining - C000T4f16M" {
    requires("1.31g");
    run("8-bit");
    setAutoThreshold("Minimum");
	run("Make Binary");
	run("Invert");
	run("Set Scale...", "distance=214 known=50 unit=µm global");
    run("Set Measurements...", "area perimeter area_fraction shape");
    run("Analyze Particles...", "size=2.5-infinity minimum=50 show=Overlay add display exclude include clear record");
    
    n = nResults;
    	
    deleted_entries = 0;
    
    for (i=n-1; i>=0; i--) {
	    if ((getResult('XStart', i)>2260) && (getResult('YStart', i)>1565))	{
			roiManager("select", i);
			roiManager("Delete");
			deleted_entries++;
		}
	}
	showMessage("deleted:"+deleted_entries);
	n = n-deleted_entries;
    
    // Arrays to store original and convex hull measurements    
    area1 = newArray(n);
    areaFrac = newArray(n);
    length1 = newArray(n);
    area2 = newArray(n);
    length2 = newArray(n);
    xstart = newArray(n);
    ystart = newArray(n);
    roundness = newArray(n);

    // Store results from original analysis
    for (i=0; i<n; i++) {
        area1[i] = getResult('Area', i);
        areaFrac[i] = getResult('\%Area',i);
        length1[i] = getResult('Perim.', i);
        xstart[i] = getResult('XStart', i);
    	ystart[i] = getResult('YStart', i);
        roundness[i] = getResult('Round', i);
     }
    
    // Clear Results Table
    run("Clear Results");
    run("Set Measurements...", "area perimeter");

    
    // Measure convex hull properties
    for (i=0; i<n; i++) {
        doWand(xstart[i], ystart[i]);
        run("Convex Hull");
        run("Measure");
        area2[i] = getResult('Area', i);
        length2[i] = getResult('Perim.', i);
    }
    //run("Clear Results");

 	run("Select None");
    // Build final results table
    for (i=0; i<n; i++) {
        setResult("Area", i, area1[i]);
        setResult("%Area", i, areaFrac[i]);
        setResult("Perim.", i, length1[i]);
        setResult("Convex Area", i, area2[i]);
        setResult("Convex Perimeter", i, length2[i]);
        setResult("Solidity", i, area1[i]/area2[i]);
        setResult("Convexity", i, length1[i]/length2[i]);
        setResult("Roundness", i, roundness[i]);
        setResult("XStart", i, xstart[i]);
        setResult("YStart", i, ystart[i]);
    }
	

	image_name = getTitle();
    updateResults();
    //run("Read and Write Excel", "file=[C:/Studium/Johann/Partikeluntersuchungen/Mining_Test.xlsx] sheet=["+image_name+"] dataset_label=Table no_count_column stack_results");
    //run("Read and Write Excel", "file=[C:/Studium/Johann/Partikeluntersuchungen/Mining_Test.xlsx] sheet=[A] dataset_label=Table no_count_column stack_results");
}
