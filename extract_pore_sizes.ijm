IJ.log("DEBUG: Running macro");
args = getArgument();
argArray = split(args, ",");
pixels = 214;
microns = 50;

run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");
run("Set Scale...", "distance="+pixels+" known="+microns+" unit=µm global");
run("Set Measurements...", "area perimeter area_fraction fit shape feret's");
run("Analyze Particles...", "size=100-infinity minimum=50 show=Overlay add display exclude include clear record");

n = nResults;
	
deleted_entries = 0;

// excluding the scale bar
for (i=n-1; i>=0; i--) {
    if ((getResult('XStart', i)>2260) && (getResult('YStart', i)>1565))	{
		roiManager("select", i);
		roiManager("Delete");
		deleted_entries++;
	}
}
n = n-deleted_entries;

// Arrays to store all pore data for final distribution
allPoreAreas = newArray(0);
allPoreParticleIDs = newArray(0);
totalPoreCount = 0;

// Get original image for pore extraction
originalID = getImageID();
originalTitle = getTitle();

print("\\Clear");
print("=== PORE SIZE EXTRACTION ANALYSIS ===");
print("Image: " + originalTitle);
print("Found " + n + " particles for pore analysis");
print("");

// Process each particle to extract pores
for (i=0; i<n; i++) {
    print("Processing particle " + (i+1) + " of " + n + "...");

    // Select the particle ROI
    roiManager("select", i);
    Roi.getBounds(x, y, width, height);

    // Create a working image for this particle
    selectImage(originalID);
    roiManager("select", i);
    run("Duplicate...", "title=Particle_" + (i+1));
    particleID = getImageID();

    // Clear outside the particle to isolate it
    run("Clear Outside");

    // Invert the image so pores (holes) become white particles
    run("Invert");

    // Analyze pores within this particle
    run("Set Measurements...", "area centroid redirect=None decimal=3");
    run("Analyze Particles...", "size=1-Infinity display exclude clear add");

    nPores = nResults;

    if (nPores > 0) {
        print("  Found " + nPores + " pores in particle " + (i+1));

        // Store pore data for this particle
        for (j = 0; j < nPores; j++) {
            poreArea = getResult("Area", j);

            // Expand arrays to store new pore data
            allPoreAreas = Array.concat(allPoreAreas, poreArea);
            allPoreParticleIDs = Array.concat(allPoreParticleIDs, i+1);
            totalPoreCount++;

            print("    Pore " + (j+1) + ": Area = " + d2s(poreArea, 3) + " µm²");
        }
    } else {
        print("  No pores found in particle " + (i+1));
    }

    // Clean up - close the working image
    selectImage(particleID);
    close();

    // Clear ROI manager for next particle
    roiManager("reset");

    // Re-add all particle ROIs for next iteration
    selectImage(originalID);
    run("Set Measurements...", "area perimeter area_fraction fit shape feret's");
    run("Analyze Particles...", "size=100-infinity minimum=50 show=Nothing add exclude include clear");

    // Remove scale bar particles again
    nROIs = roiManager("count");
    for (k = nROIs-1; k >= 0; k--) {
        roiManager("select", k);
        Roi.getBounds(rx, ry, rw, rh);
        if ((rx > 2260) && (ry > 1565)) {
            roiManager("Delete");
        }
    }
}

print("");
print("=== PORE SIZE DISTRIBUTION RESULTS ===");
print("Total particles analyzed: " + n);
print("Total pores found: " + totalPoreCount);

if (totalPoreCount > 0) {
    // Calculate statistics
    Array.getStatistics(allPoreAreas, min, max, mean, stdDev);

    print("Pore area statistics:");
    print("  Minimum: " + d2s(min, 3) + " µm²");
    print("  Maximum: " + d2s(max, 3) + " µm²");
    print("  Mean: " + d2s(mean, 3) + " µm²");
    print("  Std Dev: " + d2s(stdDev, 3) + " µm²");

    // Create detailed results table
    run("Clear Results");
    for (i = 0; i < totalPoreCount; i++) {
        setResult("Pore_ID", i, i+1);
        setResult("Particle_ID", i, allPoreParticleIDs[i]);
        setResult("Pore_Area_um2", i, allPoreAreas[i]);
    }
    updateResults();

    // Save results
    saveDir = getDirectory("image");
    if (saveDir == "") {
        saveDir = getDirectory("Choose directory to save results");
    }

    baseName = File.nameWithoutExtension;
    if (baseName == "") {
        baseName = "PoreAnalysis";
    }

    // Save detailed pore data
    saveAs("Results", saveDir + baseName + "_PoreData.csv");

    // Create and save pore size distribution histogram
    Plot.create("Pore Size Distribution", "Pore Area (µm²)", "Frequency");
    Plot.addHistogram(allPoreAreas, 20);  // 20 bins
    Plot.show();

    // Save histogram
    selectWindow("Pore Size Distribution");
    saveAs("PNG", saveDir + baseName + "_PoreSizeDistribution.png");

    print("");
    print("Results saved:");
    print("  Detailed data: " + baseName + "_PoreData.csv");
    print("  Distribution plot: " + baseName + "_PoreSizeDistribution.png");

} else {
    print("No pores found in any particles.");
}

print("");
print("=== ANALYSIS COMPLETE ===");