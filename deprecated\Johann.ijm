macro "Calculate Convexity and Solidity" {
    requires("1.31g");
    run("8-bit");
    setAutoThreshold("Minimum");
	run("Make Binary");
	run("Invert");
	run("Set Scale...", "distance=214 known=50 unit=µm global");
    run("Set Measurements...", "area perimeter area_fraction shape");
    run("Analyze Particles...", "minimum=50 show=Overlay add display exclude include clear record");
    
    n = nResults;
    
    // Arrays to store original and convex hull measurements    
    area1 = newArray(n);
    areaFrac = newArray(n);
    length1 = newArray(n);
    area2 = newArray(n);
    length2 = newArray(n);
    xstart = newArray(n);
    ystart = newArray(n);
    roundness = newArray(n);
    
    // Store results from original analysis
    for (i=0; i<n; i++) {
        area1[i] = getResult('Area', i);
        areaFrac[i] = getResult('\%Area',i);
        length1[i] = getResult('Perim.', i);
        xstart[i] = getResult('XStart', i);
        ystart[i] = getResult('YStart', i);
        roundness[i] = getResult('Round', i);
     }
    
    // Clear Results Table
    run("Clear Results");
    run("Set Measurements...", "area perimeter");

    
    // Measure convex hull properties
    for (i=0; i<n; i++) {
        doWand(xstart[i], ystart[i]);
        run("Convex Hull");
        run("Measure");
        area2[i] = getResult('Area', i);
        length2[i] = getResult('Perim.', i);
    }
    //run("Clear Results");

 	run("Select None");
    // Build final results table
    for (i=0; i<n; i++) {
        setResult("Area", i, area1[i]);
        setResult("%Area", i, areaFrac[i]);
        setResult("Perim.", i, length1[i]);
        setResult("Convex Area", i, area2[i]);
        setResult("Convex Perimeter", i, length2[i]);
        setResult("Solidity", i, area1[i]/area2[i]);
        setResult("Convexity", i, length1[i]/length2[i]);
        setResult("Roundness", i, roundness[i]);
    }

	
    updateResults();
}
