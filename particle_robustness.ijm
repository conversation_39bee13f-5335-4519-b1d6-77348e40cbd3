// ImageJ Macro for Particle Robustness Calculation
// Calculates O1 = 2*o1 / sqrt(A)
// where o1 = number of erosions to make particle disappear
// and A = particle area
// Optimized for speed and integration with other analysis scripts

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";
run("Set Scale...", "distance=1 known=1 unit=pixels");

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
maxErosions = 200;               // Maximum number of erosions to try
structuringElement = "disk";    // "disk" or "square" for erosion kernel

// Results table name for integration with other scripts
resultsTableName = "Particle_Robustness_Results";

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
originalID = getImageID();
imageWidth = getWidth();
imageHeight = getHeight();

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;
pixelsPerMicron = pixelDistance / realDistance;
minParticleAreaMicrons = minParticleAreaPixels * micronsPerPixel * micronsPerPixel;

// Create or clear the results table
if (isOpen(resultsTableName)) {
    selectWindow(resultsTableName);
    run("Close");
}
Table.create(resultsTableName);

// Print analysis info (only if not running as part of larger script)
verboseMode = !isKeyDown("shift");  // Skip verbose output when Shift is held
if (verboseMode) {
    print("\\Clear");
    print("=== PARTICLE ROBUSTNESS ANALYSIS ===");
    print("Image: " + originalTitle);
    print("Calibration: " + pixelDistance + " pixels = " + realDistance + " " + unit);
    print("Scale: " + d2s(micronsPerPixel, 4) + " " + unit + "/pixel");
    print("Minimum particle area: " + d2s(minParticleAreaMicrons, 2) + " " + unit + "²");
    print("Maximum erosions tested: " + maxErosions);
    print("");
}

// =============================================================================
// IMAGE PREPROCESSING
// =============================================================================

// Create working copy
selectImage(originalID);
run("Duplicate...", "title=Working");
workingID = getImageID();

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// Analyze particles to get basic measurements and ROIs
run("Set Measurements...", "area centroid redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude clear add Overlay");

nParticles = nResults;
if (nParticles == 0) {
    exit("No particles found above size threshold.");
}

if (verboseMode) {
    print("Found " + nParticles + " particles for robustness analysis");
}

// =============================================================================
// ROBUSTNESS CALCULATION SETUP
// =============================================================================

// Arrays to store results
robustnessValues = newArray(nParticles);
erosionCounts = newArray(nParticles);
particleAreas = newArray(nParticles);
validParticles = 0;

// Set batch mode for speed
setBatchMode(true);

// =============================================================================
// MAIN ANALYSIS LOOP
// =============================================================================

if (verboseMode) {
    print("Calculating robustness for each particle...");
}

for (p = 0; p < nParticles; p++) {
    showProgress(p, nParticles);

    // Get particle area from results table (in pixels)
    areaPixels = getResult("Area", p);
    areaMicrons = areaPixels * micronsPerPixel * micronsPerPixel;
    particleAreas[p] = areaMicrons;

    // Speed optimization: Create minimal working image for this particle only
    roiManager("select", p);
    Roi.getBounds(x, y, width, height);

    // Create smaller working image containing only the particle region
    newImage("Particle_" + (p+1), "8-bit black", width + 20, height + 20, 1);
    particleID = getImageID();

    // Copy particle from working image to smaller image
    selectImage(workingID);
    roiManager("select", p);
    run("Copy");
    selectImage(particleID);
    makeRectangle(10, 10, width, height);
    run("Paste");
    run("Select None");

    // Count erosions needed to make particle disappear
    erosionCount = 0;
    particleExists = true;

    // Speed optimization: Check if particle exists before starting erosion loop
    getHistogram(values, counts, 256);
    whitePixelCount = counts[255];

    if (whitePixelCount == 0) {
        particleExists = false;
    }

    while (particleExists && erosionCount < maxErosions) {
        // Always use disk erosion for consistency and speed
        run("Erode");
        erosionCount++;

        // Check if particle still exists using histogram method (much faster)
        getHistogram(values, counts, 256);
        whitePixelCount = counts[255];  // Count pixels with value 255 (white)

        if (whitePixelCount == 0) {
            particleExists = false;
        }
    }

    // Store results
    erosionCounts[p] = erosionCount;

    // Calculate robustness O1 = 2*o1 / sqrt(A)
    // Note: Using area in pixels for dimensional consistency
    if (areaPixels > 0) {
        robustness = (2.0 * erosionCount) / sqrt(areaPixels);
        robustnessValues[p] = robustness;
        validParticles++;
    } else {
        robustnessValues[p] = NaN;
    }

    // Clean up particle image
    selectImage(particleID);
    close();
}

// Clean up
selectImage(workingID);
close();
roiManager("reset");
setBatchMode(false);

// =============================================================================
// RESULTS CALCULATION
// =============================================================================

// Calculate statistics
robustnessSum = 0;
validCount = 0;

for (i = 0; i < nParticles; i++) {
    if (!isNaN(robustnessValues[i])) {
        robustnessSum += robustnessValues[i];
        validCount++;
    }
}

if (validCount == 0) {
    exit("Error: No valid robustness values calculated.");
}

averageRobustness = robustnessSum / validCount;

// Calculate standard deviation
varianceSum = 0;
for (i = 0; i < nParticles; i++) {
    if (!isNaN(robustnessValues[i])) {
        diff = robustnessValues[i] - averageRobustness;
        varianceSum += diff * diff;
    }
}
standardDeviation = sqrt(varianceSum / validCount);

// =============================================================================
// RESULTS OUTPUT
// =============================================================================

// Store results in separate table for integration with other scripts
selectWindow(resultsTableName);

// Create particle numbers starting from 1 to match ROI numbers
particleNumbers = newArray(nParticles);
for (i = 0; i < nParticles; i++) {
    particleNumbers[i] = i + 1;  // Add 1 to match ROI numbering
}
Table.setColumn("Particle", particleNumbers);

// Create arrays for table columns (handle NaN values)
areaColumn = newArray(nParticles);
erosionColumn = newArray(nParticles);
robustnessColumn = newArray(nParticles);

for (i = 0; i < nParticles; i++) {
    areaColumn[i] = particleAreas[i];
    erosionColumn[i] = erosionCounts[i];
    if (isNaN(robustnessValues[i])) {
        robustnessColumn[i] = 0;  // Replace NaN with 0 for table compatibility
    } else {
        robustnessColumn[i] = robustnessValues[i];
    }
}

Table.setColumn("Area_" + unit + "²", areaColumn);
Table.setColumn("Erosions_o1", erosionColumn);
Table.setColumn("Robustness_O1", robustnessColumn);
Table.update;

// Print summary to log (only in verbose mode)
if (verboseMode) {
    print("");
    print("=== RESULTS ===");
    print("Valid particles analyzed: " + validCount + " / " + nParticles);
    print("Average robustness O1: " + d2s(averageRobustness, 4));
    print("Standard deviation: " + d2s(standardDeviation, 4));
    print("Range: " + d2s(averageRobustness - standardDeviation, 3) + " - " + d2s(averageRobustness + standardDeviation, 3));
    print("");
    print("=== INTERPRETATION ===");
    print("Higher O1 values indicate more robust (thicker) particles");
    print("Lower O1 values indicate more fragile (thinner) particles");
    print("O1 is dimensionless and scale-invariant");
    print("");
    print("Analysis complete! Results stored in '" + resultsTableName + "' table.");
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

// Helper function to find minimum value in array (excluding NaN)
function getMinValue(array) {
    minVal = 1e10;
    for (i = 0; i < array.length; i++) {
        if (!isNaN(array[i]) && array[i] < minVal) {
            minVal = array[i];
        }
    }
    return minVal;
}

// Helper function to find maximum value in array (excluding NaN)
function getMaxValue(array) {
    maxVal = -1e10;
    for (i = 0; i < array.length; i++) {
        if (!isNaN(array[i]) && array[i] > maxVal) {
            maxVal = array[i];
        }
    }
    return maxVal;
}
