// ImageJ Macro for Skeleton Analysis of Particles
// This macro analyzes particle skeletons to distinguish satellites from splattered particles

// Initialize results table
if (isOpen("Skeleton Results")) {
    selectWindow("Skeleton Results");
    run("Close");
}
IJ.log("\\Clear");

// Create results table
Table.create("Skeleton Results");

// Get current image
originalID = getImageID();
originalTitle = getTitle();

// Ensure image is binary
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");
run("Set Scale...", "distance=214 known=50 unit=µm global");

// Analyze particles and get individual particles
run("Set Measurements...", "area centroid perimeter bounding redirect=None decimal=3");
run("Analyze Particles...", "size=100-Infinity exclude add");

// Get number of particles
nParticles = roiManager("count");
print("Found " + nParticles + " particles");

// Process each particle
for (i = 0; i < nParticles; i++) {
    // Select original image
    selectImage(originalID);
    
    // Select current particle ROI
    roiManager("select", i);
    
    // Duplicate the particle
    run("Duplicate...", "title=Particle_" + i);
    particleID = getImageID();
    
    // Clear outside to isolate the particle
    run("Clear Outside");
    
    // Fill holes to ensure solid particle
    run("Fill Holes");
    
    // Create skeleton
    run("Skeletonize");
    
    // Analyze skeleton
    skeletonResults = analyzeParticleSkeleton(i);
    
    // Add results to table
    addSkeletonResultsToTable(i, skeletonResults);

    // Debug: Print some key values
    if (i < 3) { // Only for first few particles to avoid spam
        print("Particle " + i + ": Area=" + skeletonResults[1] + ", Skeleton_Length=" + skeletonResults[3] + ", End_Points=" + skeletonResults[4]);
    }

    // Close particle image
    close();
    
    // Update progress
    if (i % 10 == 0) {
        print("Processed " + (i+1) + "/" + nParticles + " particles");
    }
}

// Clean up
//roiManager("reset");
selectImage(originalID);

// Show results
Table.update("Skeleton Results");

// Debug: Check table size before classification
nRows = Table.size("Skeleton Results");
print("Table has " + nRows + " rows before classification features calculation");

// Calculate classification features
calculateClassificationFeatures();

print("Skeleton analysis complete!");

// Function to analyze skeleton of a single particle
function analyzeParticleSkeleton(particleIndex) {
    // Get basic particle measurements
    run("Set Measurements...", "area perimeter bounding redirect=None decimal=3");
    run("Measure");
    area = getResult("Area", nResults-1);
    perimeter = getResult("Perimeter", nResults-1);
    
    // Count skeleton pixels
    skeletonPixels = countSkeletonPixels();
    
    // Analyze skeleton topology
    endPoints = countEndPoints();
    branchPoints = countBranchPoints();
    
    // Calculate longest path through skeleton
    longestPath = calculateLongestPath();
    
    // Calculate branch length statistics
    branchStats = analyzeBranchLengths(skeletonPixels, endPoints, branchPoints);
    
    // Calculate skeleton complexity measures
    complexity = calculateComplexityMeasures(skeletonPixels, endPoints, branchPoints, area);
    
    // Return comprehensive results
    results = newArray(
        particleIndex,           // Particle ID
        area,                   // Original area
        perimeter,              // Original perimeter
        skeletonPixels,         // Total skeleton length
        endPoints,              // Number of end points
        branchPoints,           // Number of branch points
        longestPath,            // Longest path length
        branchStats[0],         // Average branch length
        branchStats[1],         // Branch length std dev
        branchStats[2],         // Number of branches
        complexity[0],          // Skeleton density (skeleton/area)
        complexity[1],          // Branch density (branches/area)
        complexity[2],          // Complexity index
        complexity[3]           // Satellite likelihood score
    );
    
    return results;
}

// Count skeleton pixels (skeleton length)
function countSkeletonPixels() {
    // Count white pixels in skeleton
    getRawStatistics(nPixels, mean, min, max, std, histogram);
    if (histogram.length > 1) {
        return histogram[255]; // White pixels
    }
    return 0;
}

// Count end points in skeleton
function countEndPoints() {
    // Duplicate for analysis
    run("Duplicate...", "title=EndPoints");
    
    // Apply end point detection
    // This uses a series of hit-or-miss operations
    endPointCount = 0;
    
    // Define 8 end-point kernels for different orientations
    kernels = newArray(
        "0 0 0 0 1 0 1 1 1",  // End point patterns
        "0 0 0 0 1 1 0 1 1",
        "0 0 0 1 1 0 1 1 0",
        "1 0 0 1 1 0 1 0 0",
        "1 1 0 1 1 0 0 0 0",
        "1 1 1 0 1 0 0 0 0",
        "0 1 1 0 1 1 0 0 0",
        "0 1 1 1 1 0 0 0 1"
    );
    
    // Simple end point detection: pixels with only one neighbor
    width = getWidth();
    height = getHeight();
    
    for (y = 1; y < height-1; y++) {
        for (x = 1; x < width-1; x++) {
            if (getPixel(x, y) == 255) { // If skeleton pixel
                neighbors = 0;
                // Check 8-neighborhood
                for (dy = -1; dy <= 1; dy++) {
                    for (dx = -1; dx <= 1; dx++) {
                        if (dx == 0 && dy == 0) continue;
                        if (getPixel(x+dx, y+dy) == 255) {
                            neighbors++;
                        }
                    }
                }
                // End point has exactly 1 neighbor
                if (neighbors == 1) {
                    endPointCount++;
                }
            }
        }
    }
    
    close(); // Close EndPoints image
    return endPointCount;
}

// Count branch points (junctions) in skeleton
function countBranchPoints() {
    // Duplicate for analysis
    run("Duplicate...", "title=BranchPoints");
    
    width = getWidth();
    height = getHeight();
    branchPointCount = 0;
    
    for (y = 1; y < height-1; y++) {
        for (x = 1; x < width-1; x++) {
            if (getPixel(x, y) == 255) { // If skeleton pixel
                neighbors = 0;
                // Check 8-neighborhood
                for (dy = -1; dy <= 1; dy++) {
                    for (dx = -1; dx <= 1; dx++) {
                        if (dx == 0 && dy == 0) continue;
                        if (getPixel(x+dx, y+dy) == 255) {
                            neighbors++;
                        }
                    }
                }
                // Branch point has 3 or more neighbors
                if (neighbors >= 3) {
                    branchPointCount++;
                }
            }
        }
    }
    
    close(); // Close BranchPoints image
    return branchPointCount;
}

// Calculate longest path through skeleton
function calculateLongestPath() {
    // Simplified longest path calculation
    // In a full implementation, you'd use Dijkstra's algorithm
    // Here we approximate using distance transform
    
    run("Duplicate...", "title=LongestPath");
    
    // Distance transform
    run("Distance Map");
    
    // Find maximum distance
    getRawStatistics(nPixels, mean, min, max, std, histogram);
    longestDistance = max * 2; // Approximate longest path
    
    close(); // Close LongestPath image
    return longestDistance;
}

// Analyze branch lengths
function analyzeBranchLengths(skeletonPixels, endPoints, branchPoints) {
    // Simplified branch analysis
    // Returns [average_length, std_dev, branch_count]

    // Estimate number of branches
    estimatedBranches = maxOf(1, endPoints - 1);

    // Estimate average branch length
    if (estimatedBranches > 0) {
        avgBranchLength = skeletonPixels / estimatedBranches;
    } else {
        avgBranchLength = skeletonPixels;
    }

    // Simplified std dev estimation
    stdDev = avgBranchLength * 0.3; // Rough approximation

    return newArray(avgBranchLength, stdDev, estimatedBranches);
}

// Calculate complexity measures
function calculateComplexityMeasures(skeletonPixels, endPoints, branchPoints, area) {
    // Skeleton density (skeleton length per unit area)
    if (area > 0) {
        skeletonDensity = skeletonPixels / area;
    } else {
        skeletonDensity = 0;
    }

    // Branch density (branches per unit area)
    if (area > 0) {
        branchDensity = branchPoints / area;
    } else {
        branchDensity = 0;
    }

    // Complexity index (combination of endpoints and branches)
    if (skeletonPixels > 0) {
        complexityIndex = (endPoints + branchPoints * 2) / skeletonPixels;
    } else {
        complexityIndex = 0;
    }
    
    // Satellite likelihood score
    // Satellites typically have: few endpoints (2-4), few branches (0-2), moderate skeleton density
    satelliteLikelihood = 0;
    
    // Score based on endpoint count (satellites prefer 2-4 endpoints)
    if (endPoints >= 2 && endPoints <= 4) {
        satelliteLikelihood += 0.4;
    } else if (endPoints > 4) {
        satelliteLikelihood -= 0.2;
    }
    
    // Score based on branch points (satellites prefer 0-2 branch points)
    if (branchPoints <= 2) {
        satelliteLikelihood += 0.3;
    } else {
        satelliteLikelihood -= 0.1 * (branchPoints - 2);
    }
    
    // Score based on complexity (satellites prefer lower complexity)
    if (complexityIndex < 0.1) {
        satelliteLikelihood += 0.3;
    } else {
        satelliteLikelihood -= complexityIndex;
    }
    
    // Normalize to 0-1 range
    satelliteLikelihood = maxOf(0, minOf(1, satelliteLikelihood));
    
    return newArray(skeletonDensity, branchDensity, complexityIndex, satelliteLikelihood);
}

// Add results to table
function addSkeletonResultsToTable(particleIndex, results) {
    Table.set("Particle_ID", particleIndex, results[0], "Skeleton Results");
    Table.set("Area", particleIndex, results[1], "Skeleton Results");
    Table.set("Perimeter", particleIndex, results[2], "Skeleton Results");
    Table.set("Skeleton_Length", particleIndex, results[3], "Skeleton Results");
    Table.set("End_Points", particleIndex, results[4], "Skeleton Results");
    Table.set("Branch_Points", particleIndex, results[5], "Skeleton Results");
    Table.set("Longest_Path", particleIndex, results[6], "Skeleton Results");
    Table.set("Avg_Branch_Length", particleIndex, results[7], "Skeleton Results");
    Table.set("Branch_Length_StdDev", particleIndex, results[8], "Skeleton Results");
    Table.set("Branch_Count", particleIndex, results[9], "Skeleton Results");
    Table.set("Skeleton_Density", particleIndex, results[10], "Skeleton Results");
    Table.set("Branch_Density", particleIndex, results[11], "Skeleton Results");
    Table.set("Complexity_Index", particleIndex, results[12], "Skeleton Results");
    Table.set("Satellite_Likelihood", particleIndex, results[13], "Skeleton Results");
}

// Calculate additional classification features
function calculateClassificationFeatures() {
    // Get number of rows
    nRows = Table.size("Skeleton Results");

    // Calculate additional derived features
    for (i = 0; i < nRows; i++) {
        // Endpoint to branch point ratio
        endPoints = Table.get("End_Points", i, "Skeleton Results");
        branchPoints = Table.get("Branch_Points", i, "Skeleton Results");
        if (branchPoints > 0) {
            ratio = endPoints / branchPoints;
        } else {
            ratio = endPoints;
        }
        Table.set("EndPoint_BranchPoint_Ratio", i, ratio, "Skeleton Results");

        // Skeleton efficiency (longest path / total skeleton)
        longestPath = Table.get("Longest_Path", i, "Skeleton Results");
        skeletonLength = Table.get("Skeleton_Length", i, "Skeleton Results");
        if (skeletonLength > 0) {
            efficiency = longestPath / skeletonLength;
        } else {
            efficiency = 0;
        }
        Table.set("Skeleton_Efficiency", i, efficiency, "Skeleton Results");

        // Shape factor (perimeter^2 / area)
        perimeter = Table.get("Perimeter", i, "Skeleton Results");
        area = Table.get("Area", i, "Skeleton Results");
        if (area > 0) {
            shapeFactor = (perimeter * perimeter) / area;
        } else {
            shapeFactor = 0;
        }
        Table.set("Shape_Factor", i, shapeFactor, "Skeleton Results");

        // Classification prediction (simple rule-based)
        satelliteLikelihood = Table.get("Satellite_Likelihood", i, "Skeleton Results");
        complexityIndex = Table.get("Complexity_Index", i, "Skeleton Results");

        predictedClass = "Splattered";
        if (satelliteLikelihood > 0.6 && complexityIndex < 0.15 && endPoints <= 6) {
            predictedClass = "Satellite";
        }
        Table.set("Predicted_Class", i, predictedClass, "Skeleton Results");
    }

    Table.update("Skeleton Results");
}

// Helper functions
function maxOf(a, b) {
    if (a > b) {
        return a;
    } else {
        return b;
    }
}

function minOf(a, b) {
    if (a < b) {
        return a;
    } else {
        return b;
    }
}