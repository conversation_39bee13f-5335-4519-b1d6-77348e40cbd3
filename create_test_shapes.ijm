// ImageJ Macro to Create Test Shapes for Concavity Validation
// Creates various geometric shapes with known theoretical concavity values
// Use this to validate the average_concavity.ijm macro

// =============================================================================
// TEST PARAMETERS
// =============================================================================

// Image settings
imageWidth = 1000;
imageHeight = 800;
backgroundColor = 0;    // Black background
shapeColor = 255;      // White shapes

// Calibration (same as in average_concavity.ijm)
pixelDistance = 214;   // pixels
realDistance = 50;     // micrometers
micronsPerPixel = realDistance / pixelDistance;

// Shape sizes (in pixels)
circleRadius = 50;     // Should give concavity ≈ 1.000
ellipseA = 60;         // Major axis
ellipseB = 30;         // Minor axis - should give concavity > 1.000
squareSize = 80;       // Side length
rectWidth = 100;
rectHeight = 40;
triangleSize = 80;

print("\\Clear");
print("=== CREATING TEST SHAPES FOR CONCAVITY VALIDATION ===");
print("Calibration: " + pixelDistance + " pixels = " + realDistance + " µm");
print("Expected theoretical concavity values:");
print("");

// =============================================================================
// CREATE TEST IMAGE 1: CIRCLES
// =============================================================================

newImage("Test_Circles", "8-bit black", imageWidth, imageHeight, 1);
setForegroundColor(shapeColor, shapeColor, shapeColor);

// Create circles of different sizes
radii = newArray(30, 40, 50, 60, 70);
xPositions = newArray(150, 300, 450, 600, 750);
yPosition = 200;

print("--- CIRCLES ---");
for (i = 0; i < radii.length; i++) {
    r = radii[i];
    x = xPositions[i];
    
    // Draw filled circle
    makeOval(x - r, yPosition - r, 2 * r, 2 * r);
    fill();
    
    // Calculate theoretical values
    circumference = 2 * PI * r;
    perimEquivDiam = circumference / PI;  // = 2 * r
    avgFeret = 2 * r;  // For circle, average Feret = diameter
    theoreticalConcavity = avgFeret / perimEquivDiam;  // = 1.000
    
    diameterMicrons = 2 * r * micronsPerPixel;
    print("Circle " + (i+1) + ": Diameter = " + d2s(diameterMicrons, 1) + " µm, Expected concavity = " + d2s(theoreticalConcavity, 3));
}

run("Select None");
print("All circles should show concavity ≈ 1.000");
print("");

// =============================================================================
// CREATE TEST IMAGE 2: ELLIPSES
// =============================================================================

newImage("Test_Ellipses", "8-bit black", imageWidth, imageHeight, 1);
setForegroundColor(shapeColor, shapeColor, shapeColor);

// Create ellipses with different aspect ratios
aspectRatios = newArray(1.5, 2.0, 2.5, 3.0, 4.0);
xPositions = newArray(150, 300, 450, 600, 750);
yPosition = 200;
baseRadius = 40;

print("--- ELLIPSES ---");
for (i = 0; i < aspectRatios.length; i++) {
    ratio = aspectRatios[i];
    x = xPositions[i];
    
    // Calculate ellipse dimensions
    a = baseRadius * sqrt(ratio);  // Semi-major axis
    b = baseRadius / sqrt(ratio);  // Semi-minor axis
    
    // Draw filled ellipse
    makeOval(x - a, yPosition - b, 2 * a, 2 * b);
    fill();
    
    // Calculate theoretical concavity (approximation)
    // For ellipse: average Feret ≈ (major + minor axis) / 2
    // Perimeter ≈ π * (3(a+b) - sqrt((3a+b)(a+3b))) (Ramanujan's approximation)
    majorAxis = 2 * a;
    minorAxis = 2 * b;
    avgFeretApprox = (majorAxis + minorAxis) / 2;
    
    // Simplified perimeter approximation
    perimeterApprox = PI * (3 * (a + b) - sqrt((3 * a + b) * (a + 3 * b)));
    perimEquivDiam = perimeterApprox / PI;
    theoreticalConcavity = avgFeretApprox / perimEquivDiam;
    
    majorAxisMicrons = majorAxis * micronsPerPixel;
    minorAxisMicrons = minorAxis * micronsPerPixel;
    print("Ellipse " + (i+1) + ": " + d2s(majorAxisMicrons, 1) + "×" + d2s(minorAxisMicrons, 1) + " µm, Ratio = " + d2s(ratio, 1) + ", Expected concavity ≈ " + d2s(theoreticalConcavity, 3));
}

run("Select None");
print("Ellipses should show concavity > 1.000 (increasing with aspect ratio)");
print("");

// =============================================================================
// CREATE TEST IMAGE 3: RECTANGLES
// =============================================================================

newImage("Test_Rectangles", "8-bit black", imageWidth, imageHeight, 1);
setForegroundColor(shapeColor, shapeColor, shapeColor);

// Create rectangles with different aspect ratios
widths = newArray(60, 80, 100, 120, 140);
heights = newArray(60, 50, 40, 35, 30);
xPositions = newArray(150, 300, 450, 600, 750);
yPosition = 200;

print("--- RECTANGLES ---");
for (i = 0; i < widths.length; i++) {
    w = widths[i];
    h = heights[i];
    x = xPositions[i];
    
    // Draw filled rectangle
    makeRectangle(x - w/2, yPosition - h/2, w, h);
    fill();
    
    // Calculate theoretical values
    perimeter = 2 * (w + h);
    perimEquivDiam = perimeter / PI;
    
    // For rectangle: average Feret is complex, but approximately:
    // Feret varies from min(w,h) to sqrt(w²+h²)
    minFeret = minOf(w, h);
    maxFeret = sqrt(w * w + h * h);
    avgFeretApprox = (minFeret + maxFeret) / 2;  // Rough approximation
    
    theoreticalConcavity = avgFeretApprox / perimEquivDiam;
    
    widthMicrons = w * micronsPerPixel;
    heightMicrons = h * micronsPerPixel;
    aspectRatio = maxOf(w, h) / minOf(w, h);
    print("Rectangle " + (i+1) + ": " + d2s(widthMicrons, 1) + "×" + d2s(heightMicrons, 1) + " µm, Ratio = " + d2s(aspectRatio, 1) + ", Expected concavity ≈ " + d2s(theoreticalConcavity, 3));
}

run("Select None");
print("Rectangles should show concavity > 1.000 (higher for more elongated shapes)");
print("");

// =============================================================================
// CREATE TEST IMAGE 4: MIXED SHAPES
// =============================================================================

newImage("Test_Mixed_Shapes", "8-bit black", imageWidth, imageHeight, 1);
setForegroundColor(shapeColor, shapeColor, shapeColor);

print("--- MIXED SHAPES ---");

// Perfect circle
x1 = 150; y1 = 150;
r = 40;
makeOval(x1 - r, y1 - r, 2 * r, 2 * r);
fill();
print("Circle: Expected concavity = 1.000");

// Square
x2 = 350; y2 = 150;
s = 70;
makeRectangle(x2 - s/2, y2 - s/2, s, s);
fill();
squarePerim = 4 * s;
squarePerimEquiv = squarePerim / PI;
squareDiagonal = s * sqrt(2);
squareAvgFeret = (s + squareDiagonal) / 2;  // Approximation
squareConcavity = squareAvgFeret / squarePerimEquiv;
print("Square: Expected concavity ≈ " + d2s(squareConcavity, 3));

// Elongated ellipse
x3 = 550; y3 = 150;
a = 60; b = 20;
makeOval(x3 - a, y3 - b, 2 * a, 2 * b);
fill();
ellipseAvgFeret = (2 * a + 2 * b) / 2;
ellipsePerim = PI * (3 * (a + b) - sqrt((3 * a + b) * (a + 3 * b)));
ellipsePerimEquiv = ellipsePerim / PI;
ellipseConcavity = ellipseAvgFeret / ellipsePerimEquiv;
print("Elongated ellipse: Expected concavity ≈ " + d2s(ellipseConcavity, 3));

// Triangle (equilateral)
x4 = 750; y4 = 200;
triangleSize = 80;
height = triangleSize * sqrt(3) / 2;
makePolygon(newArray(x4, x4 - triangleSize/2, x4 + triangleSize/2), 
           newArray(y4 - height/2, y4 + height/2, y4 + height/2));
fill();
trianglePerim = 3 * triangleSize;
trianglePerimEquiv = trianglePerim / PI;
triangleHeight = triangleSize * sqrt(3) / 2;
triangleAvgFeret = (triangleSize + triangleHeight) / 2;  // Approximation
triangleConcavity = triangleAvgFeret / trianglePerimEquiv;
print("Equilateral triangle: Expected concavity ≈ " + d2s(triangleConcavity, 3));

run("Select None");
print("");

// =============================================================================
// CREATE TEST IMAGE 5: SIZE VALIDATION
// =============================================================================

newImage("Test_Size_Validation", "8-bit black", imageWidth, imageHeight, 1);
setForegroundColor(shapeColor, shapeColor, shapeColor);

print("--- SIZE VALIDATION (All Circles) ---");
print("Use this to verify calibration accuracy:");

// Create circles with known diameters in micrometers
targetDiametersMicrons = newArray(10, 20, 30, 40, 50);  // µm
xPositions = newArray(150, 300, 450, 600, 750);
yPosition = 200;

for (i = 0; i < targetDiametersMicrons.length; i++) {
    diameterMicrons = targetDiametersMicrons[i];
    diameterPixels = diameterMicrons / micronsPerPixel;
    radiusPixels = diameterPixels / 2;
    x = xPositions[i];
    
    makeOval(x - radiusPixels, yPosition - radiusPixels, diameterPixels, diameterPixels);
    fill();
    
    print("Circle " + (i+1) + ": Target = " + diameterMicrons + " µm (" + d2s(diameterPixels, 1) + " pixels)");
}

run("Select None");
print("");
print("=== TESTING INSTRUCTIONS ===");
print("1. Run average_concavity.ijm on each test image");
print("2. Compare measured concavity with expected values above");
print("3. For circles: concavity should be very close to 1.000");
print("4. For elongated shapes: concavity should be > 1.000");
print("5. Check that measured diameters match target values in size validation");
print("");
print("Test images created successfully!");
print("Switch between images using the Image menu or window tabs.");
