// ----- Step 0: Get a List of Open Images -----
openImages = getList("image.titles");

// Check if at least two images are open
if (lengthOf(openImages) < 2) {
    exit("Error: You need to have at least two images open.");
}

// ----- Step 1: Create a Dialog to Select Images -----
Dialog.create("Select Images for Matching");
Dialog.addChoice("Large Image:", openImages, openImages[0]);
Dialog.addChoice("Small Image:", openImages, openImages[1]);
Dialog.show();

// Get user selections
largeImageTitle = Dialog.getChoice();
smallImageTitle = Dialog.getChoice();

// ----- Step 2: Select and Duplicate the Chosen Images -----
selectWindow(largeImageTitle);
run("Duplicate...", "title=Large_Image");

selectWindow(smallImageTitle);
run("Duplicate...", "title=Small_Image");

// // ----- Step 3: Template Matching -----
// selectWindow("Large_Image");
// run("Template Matching", "template=Small_Image correlation=Normalized_Correlation");

// resultWindow = "Correlation Map of Small Image in Large Image";
// selectWindow(resultWindow);
// run("Enhance Contrast", "saturated=0.35"); // Optional for better visualization

// Optional: Apply threshold to find multiple regions of interest in the Correlation Map
// Threshold can be adjusted to control the strictness of matches
// setAutoThreshold(resultWindow, "MaxEntropy dark");
// run(resultWindow, "Analyze Particles...", "display add");

// ----- Step 4: Keypoint Detection using FeatureJ -----
selectWindow("Small_Image");
run("FeatureJ SIFT", "compute orientation and scale"); // Detect keypoints in the small image

selectWindow("Large_Image");
run("FeatureJ SIFT", "compute orientation and scale"); // Detect keypoints in the large image

// ----- Step 5: Match Keypoints and Draw Boundaries -----
run("FeatureJ Register", "source=Small Image target=Large Image method=RANSAC"); // Match points with RANSAC

// Optional: Visualize matched points and transformation
run("Draw Transformed Rectangle");
