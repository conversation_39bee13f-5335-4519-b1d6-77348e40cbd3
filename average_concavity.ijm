// Fresh ImageJ Macro for Average Particle Concavity Calculation
// Calculates concavity = integrated Feret diameter / perimeter-equivalent diameter
// Author: Created from scratch with proper calibration handling

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";
run("Set Scale...", "distance=1 known=1 unit=pixels");

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
angleStepDegrees = 5;           // Angle step for Feret diameter calculation
useSubpixelRefinement = true;   // Enable subpixel boundary refinement

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
imageWidth = getWidth();
imageHeight = getHeight();

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;
pixelsPerMicron = pixelDistance / realDistance;
minParticleAreaMicrons = minParticleAreaPixels * micronsPerPixel * micronsPerPixel;

// Print calibration info
print("\\Clear");
print("=== AVERAGE CONCAVITY ANALYSIS ===");
print("Image: " + originalTitle);
print("Calibration: " + pixelDistance + " pixels = " + realDistance + " " + unit);
print("Scale: " + d2s(micronsPerPixel, 4) + " " + unit + "/pixel");
print("Minimum particle area: " + d2s(minParticleAreaMicrons, 2) + " " + unit + "²");
print("");

// =============================================================================
// IMAGE PREPROCESSING
// =============================================================================

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// Analyze particles to get basic measurements and ROIs
run("Set Measurements...", "area perimeter centroid redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude clear add");

nParticles = nResults;
if (nParticles == 0) {
    exit("No particles found above size threshold.");
}

print("Found " + nParticles + " particles for analysis");

// =============================================================================
// CONCAVITY CALCULATION SETUP
// =============================================================================

// Pre-calculate trigonometric values for efficiency
numAngles = 180 / angleStepDegrees;
cosValues = newArray(numAngles);
sinValues = newArray(numAngles);

for (i = 0; i < numAngles; i++) {
    angleRad = i * angleStepDegrees * PI / 180;
    cosValues[i] = cos(angleRad);
    sinValues[i] = sin(angleRad);
}

// Arrays to store results for each particle
concavityValues = newArray(nParticles);
particleAreas = newArray(nParticles);
particlePerimeters = newArray(nParticles);
avgFeretValues = newArray(nParticles);
perimEquivDiamValues = newArray(nParticles);
validParticles = 0;

// =============================================================================
// MAIN ANALYSIS LOOP
// =============================================================================

print("Calculating concavity for each particle...");

for (p = 0; p < nParticles; p++) {
    showProgress(p, nParticles);
    
    // Get basic measurements from results table (in pixels)
    areaPixels = getResult("Area", p);
    perimeterPixels = getResult("Perim.", p);
    
    // Convert to calibrated units
    areaMicrons = areaPixels * micronsPerPixel * micronsPerPixel;
    perimeterMicrons = perimeterPixels * micronsPerPixel;
    
    // Calculate perimeter-equivalent diameter
    perimeterEquivDiameterMicrons = perimeterMicrons / PI;
    
    // Select particle ROI
    roiManager("select", p);
    
    // Get particle boundary coordinates (in pixels)
    Roi.getCoordinates(xPixels, yPixels);
    nPoints = xPixels.length;
    
    if (nPoints < 3) {
        concavityValues[p] = NaN;
        continue;
    }
    
    // Apply subpixel refinement if enabled
    if (useSubpixelRefinement) {
        refineParticleBoundary(xPixels, yPixels);
    }
    
    // Convert coordinates to calibrated units (micrometers)
    xMicrons = newArray(nPoints);
    yMicrons = newArray(nPoints);
    for (i = 0; i < nPoints; i++) {
        xMicrons[i] = xPixels[i] * micronsPerPixel;
        yMicrons[i] = yPixels[i] * micronsPerPixel;
    }
    
    // Calculate integrated Feret diameter
    feretSum = 0;
    
    for (angleIdx = 0; angleIdx < numAngles; angleIdx++) {
        cosVal = cosValues[angleIdx];
        sinVal = sinValues[angleIdx];
        
        // Project all points onto the current direction
        minProjection = 1e10;
        maxProjection = -1e10;
        
        for (i = 0; i < nPoints; i++) {
            projection = xMicrons[i] * cosVal + yMicrons[i] * sinVal;
            if (projection < minProjection) minProjection = projection;
            if (projection > maxProjection) maxProjection = projection;
        }
        
        feretDiameter = maxProjection - minProjection;
        feretSum += feretDiameter;
    }
    
    // Calculate average Feret diameter
    avgFeretDiameter = feretSum / numAngles;
    
    // Calculate concavity
    concavity = avgFeretDiameter / perimeterEquivDiameterMicrons;

    // Store all results for this particle
    concavityValues[p] = concavity;
    particleAreas[p] = areaMicrons;
    particlePerimeters[p] = perimeterMicrons;
    avgFeretValues[p] = avgFeretDiameter;
    perimEquivDiamValues[p] = perimeterEquivDiameterMicrons;
    validParticles++;
}

// Clean up ROI manager
roiManager("reset");

// =============================================================================
// RESULTS CALCULATION AND OUTPUT
// =============================================================================

// Calculate average concavity
concavitySum = 0;
validCount = 0;

for (i = 0; i < nParticles; i++) {
    if (!isNaN(concavityValues[i])) {
        concavitySum += concavityValues[i];
        validCount++;
    }
}

if (validCount == 0) {
    exit("Error: No valid concavity values calculated.");
}

averageConcavity = concavitySum / validCount;

// Calculate standard deviation
varianceSum = 0;
for (i = 0; i < nParticles; i++) {
    if (!isNaN(concavityValues[i])) {
        diff = concavityValues[i] - averageConcavity;
        varianceSum += diff * diff;
    }
}
standardDeviation = sqrt(varianceSum / validCount);

// =============================================================================
// FINAL RESULTS OUTPUT
// =============================================================================

print("");
print("=== RESULTS ===");
print("Valid particles analyzed: " + validCount + " / " + nParticles);
print("Average concavity: " + d2s(averageConcavity, 4));
print("Standard deviation: " + d2s(standardDeviation, 4));
print("Range: " + d2s(averageConcavity - standardDeviation, 3) + " - " + d2s(averageConcavity + standardDeviation, 3));
print("");
print("=== INTERPRETATION ===");
print("Concavity = 1.0: Perfect circle");
print("Concavity > 1.0: Elongated/irregular shape");
print("Concavity < 1.0: Should not occur (check calibration)");

// Create detailed results table showing each particle
run("Clear Results");

// Add individual particle results
for (i = 0; i < nParticles; i++) {
    if (!isNaN(concavityValues[i])) {
        setResult("Particle", i, i + 1);
        setResult("Area_" + unit + "²", i, particleAreas[i]);
        setResult("Perimeter_" + unit, i, particlePerimeters[i]);
        setResult("Perim_Equiv_Diam_" + unit, i, perimEquivDiamValues[i]);
        setResult("Avg_Feret_" + unit, i, avgFeretValues[i]);
        setResult("Concavity", i, concavityValues[i]);
    }
}
updateResults();

// Also create a summary table in a separate window
if (isOpen("Summary")) {
    selectWindow("Summary");
    run("Close");
}

run("Table...", "name=Summary width=400 height=200");
print("[Summary]", "\\Headings:Metric\tValue");
print("[Summary]", "Average Concavity\t" + d2s(averageConcavity, 4));
print("[Summary]", "Standard Deviation\t" + d2s(standardDeviation, 4));
print("[Summary]", "Valid Particles\t" + validCount);
print("[Summary]", "Total Particles\t" + nParticles);
print("[Summary]", "Min Concavity\t" + d2s(getMinValue(concavityValues), 4));
print("[Summary]", "Max Concavity\t" + d2s(getMaxValue(concavityValues), 4));

print("");
print("Analysis complete!");

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

// Function to refine particle boundary with subpixel accuracy
function refineParticleBoundary(xCoords, yCoords) {
    nPoints = xCoords.length;
    smoothedX = newArray(nPoints);
    smoothedY = newArray(nPoints);

    // Apply Gaussian smoothing to reduce pixelation artifacts
    sigma = 0.8;  // Smoothing parameter

    for (i = 0; i < nPoints; i++) {
        weightSum = 0;
        xSum = 0;
        ySum = 0;

        // Use 5-point neighborhood for smoothing
        for (j = -2; j <= 2; j++) {
            idx = (i + j + nPoints) % nPoints;
            weight = exp(-(j * j) / (2 * sigma * sigma));
            xSum += xCoords[idx] * weight;
            ySum += yCoords[idx] * weight;
            weightSum += weight;
        }

        smoothedX[i] = xSum / weightSum;
        smoothedY[i] = ySum / weightSum;
    }

    // Copy smoothed coordinates back
    for (i = 0; i < nPoints; i++) {
        xCoords[i] = smoothedX[i];
        yCoords[i] = smoothedY[i];
    }
}

// Helper function to find minimum value in array (excluding NaN)
function getMinValue(array) {
    minVal = 1e10;
    for (i = 0; i < array.length; i++) {
        if (!isNaN(array[i]) && array[i] < minVal) {
            minVal = array[i];
        }
    }
    return minVal;
}

// Helper function to find maximum value in array (excluding NaN)
function getMaxValue(array) {
    maxVal = -1e10;
    for (i = 0; i < array.length; i++) {
        if (!isNaN(array[i]) && array[i] > maxVal) {
            maxVal = array[i];
        }
    }
    return maxVal;
}
