    var lut = -1;
    var lutdir = getDirectory("luts");
    var list;
    var color = 0;
    var colors = newArray("<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>gent<PERSON>", "<PERSON>");

    macro "Grayscale LUT Action Tool - C111F123dC444F423dC888F723dCbbbFa23dCeeeFd23d" {
        run("Grays");
        if (getWidth==256 && getHeight==32)
            rename("Grayscale");
   }

    macro "Fire LUT Action Tool - C108F123dCa0aF423dCf40F723dCfb0Fa23dCff9Fd23d" {
        run("Fire");
        if (getWidth==256 && getHeight==32)
            rename("Fire");
   }

    macro "Primary Colors Action Tool - Cf00F12f3 C0f0F17f3 C00fF1cf3" {
        run(colors[color]);
        if (getWidth==256 && getHeight==32)
            rename(colors[color]);
        showStatus(colors[color]);
        color++;
        if (color==6) color = 0;
   }

    macro "Edit LUT Action Tool - C005F1155C40cF6155Ca0bFa155Cc05F1655Cf40F6655Cf80Fa655Cfb0F1a55Cff1F6a55CffeFaa55" {
        run("Edit LUT...");
   }

    macro "Adjust Brightness and Contrast Action Tool - C037D04D05D06D07D08D09D0aD0bD0cD14D18D1cD24D28D2cD34D38D3cD45D46D47D49D4aD4bD6bD6cD76D77D78D79D7aD84D85Da6Da7Da8Da9DaaDb5DbbDc4DccDd4DdcDe5DebDf6Dfa" {
        run("Brightness/Contrast...");
   }

    macro "Previous LUT Action Tool - C037T4d14<" {
        cycleLUTs(-1);
   }

    macro "Next LUT Action Tool - C037T4d14>" {
        cycleLUTs(1);
   }

    macro "Invert LUT Action Tool - C037R12ccL12cc" {
        run("Invert LUT");
   }

  // Based on the LUTFileTool by Gabriel Landini
  function cycleLUTs(inc) {
       if (lut==-1)
           createLutList();
       if (nImages==0) {
          call("ij.gui.ImageWindow.centerNextImage");
          newImage("LUT", "8-bit ramp", 256, 32, 1);
       }
       if (bitDepth==24)
           exit("RGB images do not have LUTs");
       if (isKeyDown("alt"))
           lut = 0;
       else
          lut += inc;
      if (lut<0) lut = list.length-1;
      if (lut>list.length-1) lut = 0;
      name = list[lut];
      run("LUT... ", "open=["+lutdir+name+"]");
      name = substring(name, 0, lengthOf(name)-4);
      if (getWidth==256 && getHeight==32)
            rename(name);
      showStatus((lut+1) + ". " + name);
  }

  function createLutList() {
      err = "No LUTs in the '/ImageJ/luts' folder";
      if (!File.exists(lutdir))
           exit(err);
      rawlist = getFileList(lutdir);
      if (rawlist.length==0)
          exit(err);
      count = 0;
      for (i=0; i< rawlist.length; i++) {
          if (endsWith(rawlist[i], ".lut")) count++;
      }
      if (count==0)
          exit(err);
      list = newArray(count);
      index = 0;
      for (i=0; i< rawlist.length; i++) {
          if (endsWith(rawlist[i], ".lut"))
              list[index++] = rawlist[i];
      }
  }

  macro "Generate Icon String" {
      colors = 9;
      getLut(reds, greens, blues);
      str = "";
      for (i=0; i<colors; i++) {
          index = i*256/colors+ 128/colors;
          r = reds[index];
          g = greens[index];
          b = blues[index];
          str = str + "C"+toHex(r/16)+toHex(g/16)+toHex(b/16)+"F"+toHex(i*3+1)+"23d";
      }
      print(str);
  }
