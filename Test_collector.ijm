// Comprehensive ImageJ Macro for Particle Analysis with Classification
// Calculates all particle metrics and provides interactive classification with Excel export
// Optimized for speed and integration with larger analysis workflows

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";
run("Set Scale...", "distance=1 known=1 unit=pixels");

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
maxErosions = 200;               // Maximum number of erosions to try
angleStepDegrees = 5;           // Angle step for Feret diameter calculation
useSubpixelRefinement = true;   // Enable subpixel boundary refinement

// Results table name
combinedTableName = "Comprehensive_Particle_Analysis";

// Classification groups
groups = newArray("Round_NonPorous", "Round_Porous", "Satellite_NonPorous",
                  "Satellite_Porous", "Splattered_NonPorous", "Splattered_Porous");

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
originalID = getImageID();
displayID = 0;  // Will be set to the thresholded image ID later

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;

// Suppress verbose analysis output (as requested)
verboseMode = false;  // Always suppress verbose output during analysis

print("\\Clear");
print("=== COMPREHENSIVE PARTICLE ANALYSIS ===");
print("Image: " + originalTitle);
print("Starting analysis...");

// =============================================================================
// IMAGE PREPROCESSING
// =============================================================================

// Create working copy
selectImage(originalID);
run("Duplicate...", "title=Working");
workingID = getImageID();

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// Analyze particles to get comprehensive measurements and ROIs
run("Set Measurements...", "area perimeter area_fraction fit shape feret's centroid redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude clear add");
