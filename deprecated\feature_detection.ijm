// Set paths for the large and small images
largeImagePath = "/path/to/large_image.jpg";
smallImagePath = "/path/to/small_image.jpg";

// Open the images
open(largeImagePath);
run("Duplicate...", "title=Large Image");
selectWindow("Large Image");

open(smallImagePath);
run("Duplicate...", "title=Small Image");
selectWindow("Small Image");

// ----- Step 1: Template Matching -----
selectWindow("Large Image");
run("Template Matching", "template=Small Image correlation=Normalized_Correlation");

resultWindow = "Correlation Map of Small Image in Large Image";
selectWindow(resultWindow);
run("Enhance Contrast", "saturated=0.35"); // Optional for better visualization

// Optional: Apply threshold to find multiple regions of interest in the Correlation Map
// Threshold can be adjusted to control the strictness of matches
setAutoThreshold(resultWindow, "MaxEntropy dark");
run(resultWindow, "Analyze Particles...", "display add");

// ----- Step 2: Keypoint Detection using FeatureJ -----
selectWindow("Small Image");
run("FeatureJ SIFT", "compute orientation and scale"); // Detect keypoints in the small image

selectWindow("Large Image");
run("FeatureJ SIFT", "compute orientation and scale"); // Detect keypoints in the large image

// ----- Step 3: Match Keypoints and Draw Boundaries -----
run("FeatureJ Register", "source=Small Image target=Large Image method=RANSAC"); // Match points with RANSAC

// Optional: Visualize matched points and transformation
run("Draw Transformed Rectangle");
