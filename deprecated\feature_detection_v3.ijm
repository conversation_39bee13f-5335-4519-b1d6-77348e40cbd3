// ----- Step 0: Get a List of Open Images -----
openImages = getList("image.titles");

// Check if at least two images are open
if (lengthOf(openImages) < 2) {
    exit("Error: You need to have at least two images open.");
}

// ----- Step 1: Create a Dialog to Select Images -----
Dialog.create("Select Images for Matching");
Dialog.addChoice("Large Image:", openImages, openImages[0]);
Dialog.addChoice("Small Image:", openImages, openImages[1]);
Dialog.show();

// Get user selections
largeImageTitle = Dialog.getChoice();
smallImageTitle = Dialog.getChoice();

// ----- Step 2: Select the Chosen Images -----
selectWindow(largeImageTitle);
run("Duplicate...", "title=Large Image");

selectWindow(smallImageTitle);
run("Duplicate...", "title=Small Image");

// ----- Step 3: Use Feature Extraction to Find Correspondences -----
selectWindow("Small Image");
run("Feature Extraction", "Extract MOPS correspondences");

smallMOPSResults = getResultsTable(); // Get the results table for small image MOPS

selectWindow("Large Image");
run("Feature Extraction", "Extract MOPS correspondences");

largeMOPSResults = getResultsTable(); // Get the results table for large image MOPS

// ----- Step 4: Draw Frame Around Correspondence -----
// This assumes that the results are in the form of coordinates (x,y)
numCorrespondences = smallMOPSResults.getCounter();
if (numCorrespondences > 0) {
    // Draw rectangle around the best match
    // Here, for simplicity, we'll use the first correspondence found
    xSmall = smallMOPSResults.getValue("X", 0);
    ySmall = smallMOPSResults.getValue("Y", 0);
    xLarge = largeMOPSResults.getValue("X", 0);
    yLarge = largeMOPSResults.getValue("Y", 0);
    
    // Draw a rectangle around the matching area
    width = smallMOPSResults.getValue("Width", 0); // Assume width in small image matches area in large image
    height = smallMOPSResults.getValue("Height", 0); // Assume height in small image matches area in large image
    
    // Select the position to draw on the large image based on the match
    xDraw = xLarge - (width / 2); // Center the rectangle
    yDraw = yLarge - (height / 2); // Center the rectangle
    
    // Draw the rectangle on the large image
    makeRectangle(xDraw, yDraw, width, height);
    run("Draw");
}

// Optional: Enhance the results
selectWindow("Large Image");
run("Enhance Contrast", "saturated=0.35");
