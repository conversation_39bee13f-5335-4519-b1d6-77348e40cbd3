// ImageJ Macro for Largest Concavity Index Calculation
// Calculates O2 = 2*o2 / sqrt(A)
// where o2 = number of erosions until concave regions disappear
// and A = particle area
// Optimized for speed and integration with other analysis scripts

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";
run("Set Scale...", "distance=1 known=1 unit=pixels");

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
maxErosions = 200;               // Maximum number of erosions to try
structuringElement = "disk";    // "disk" or "square" for erosion kernel

// Results table name for integration with other scripts
resultsTableName = "Largest_Concavity_Results";

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
originalID = getImageID();
imageWidth = getWidth();
imageHeight = getHeight();

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;
pixelsPerMicron = pixelDistance / realDistance;
minParticleAreaMicrons = minParticleAreaPixels * micronsPerPixel * micronsPerPixel;

// Create or clear the results table
if (isOpen(resultsTableName)) {
    selectWindow(resultsTableName);
    run("Close");
}
Table.create(resultsTableName);

// Print calibration info (only if not running as part of larger script)
verboseMode = !isKeyDown("shift");  // Skip verbose output when Shift is held
if (verboseMode) {
    print("\\Clear");
    print("=== LARGEST CONCAVITY INDEX ANALYSIS ===");
    print("Image: " + originalTitle);
    print("Calibration: " + pixelDistance + " pixels = " + realDistance + " " + unit);
    print("Scale: " + d2s(micronsPerPixel, 4) + " " + unit + "/pixel");
    print("Minimum particle area: " + d2s(minParticleAreaMicrons, 2) + " " + unit + "²");
    print("Max erosions tested: " + maxErosions);
    print("");
}

// Create working copy
selectImage(originalID);
run("Duplicate...", "title=Working");
workingID = getImageID();

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// Analyze particles to get basic measurements and ROIs
run("Set Measurements...", "area centroid redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude clear add");

nParticles = nResults;
if (nParticles == 0) {
    exit("No particles found above size threshold.");
}

if (verboseMode) {
    print("Found " + nParticles + " particles for analysis");
}

// Arrays to store results for each particle
erosionCounts = newArray(nParticles);
concavityIndices = newArray(nParticles);
particleAreas = newArray(nParticles);
validParticles = 0;

// Speed optimization: Disable screen updates during processing
setBatchMode(true);


// =============================================================================
// MAIN ANALYSIS LOOP
// =============================================================================

if (verboseMode) {
    print("Calculating largest concavity index for each particle...");
}

for (p = 0; p < nParticles; p++) {
    showProgress(p, nParticles);

    // Get particle area from results table (in pixels)
    areaPixels = getResult("Area", p);
    areaMicrons = areaPixels * micronsPerPixel * micronsPerPixel;
    particleAreas[p] = areaMicrons;

    if (verboseMode) {
        print("Processing particle " + (p + 1) + "/" + nParticles + " (Area: " + d2s(areaMicrons, 2) + " " + unit + "²)");
    }

    // Speed optimization: Work directly with ROI coordinates instead of creating images
    roiManager("select", p);

    // Get ROI coordinates for convex hull calculation
    Roi.getCoordinates(xCoords, yCoords);
    nPoints = xCoords.length;

    if (nPoints < 3) {
        erosionCounts[p] = 0;
        concavityIndices[p] = 0;
        continue;
    }

    // Create minimal working image for this particle only
    Roi.getBounds(x, y, width, height);
    newImage("ParticleWork", "8-bit black", width + 20, height + 20, 1);
    particleWorkID = getImageID();

    // Translate ROI to fit in smaller image
    for (i = 0; i < nPoints; i++) {
        xCoords[i] = xCoords[i] - x + 10;
        yCoords[i] = yCoords[i] - y + 10;
    }
    makeSelection("polygon", xCoords, yCoords);
    run("Fill", "slice");

    // Create convex hull version
    run("Duplicate...", "title=ConvexHull");
    convexHullID = getImageID();
    run("Convex Hull");
    run("Fill", "slice");

    // Calculate difference (concave regions)
    imageCalculator("Subtract create", convexHullID, particleWorkID);
    concaveRegionsID = getImageID();

    // Clean up intermediate images immediately
    selectImage(convexHullID);
    close();
    selectImage(particleWorkID);
    close();

    // Now perform erosions on the concave regions until they disappear
    selectImage(concaveRegionsID);
    erosionCount = 0;
    particleExists = true;

    // Speed optimization: Use faster histogram check
    getHistogram(values, counts, 256);
    whitePixelCount = counts[255];

    if (whitePixelCount == 0) {
        // No concave regions - particle is already convex
        erosionCount = 0;
        particleExists = false;
        if (verboseMode) {
            print("  Particle " + (p + 1) + " is already convex (no concave regions)");
        }
    } else {
        if (verboseMode) {
            print("  Initial concave region pixels: " + whitePixelCount);
        }
    }

    // Speed optimization: Use binary erosion operations
    while (particleExists && erosionCount < maxErosions) {
        run("Erode");  // Always use disk erosion for consistency
        erosionCount++;

        // Fast check using histogram
        getHistogram(values, counts, 256);
        whitePixelCount = counts[255];

        if (whitePixelCount == 0) {
            particleExists = false;
            if (verboseMode) {
                print("  Concave regions disappeared after " + erosionCount + " erosions");
            }
        }
    }

    // Store results
    erosionCounts[p] = erosionCount;

    // Calculate largest concavity index O2 = 2*o2 / sqrt(A)
    // Note: Using area in pixels for dimensional consistency
    if (areaPixels > 0) {
        concavityIndex = (2.0 * erosionCount) / sqrt(areaPixels);
        concavityIndices[p] = concavityIndex;
        validParticles++;
        if (verboseMode) {
            print("  O2 = " + d2s(concavityIndex, 4));
        }
    } else {
        concavityIndices[p] = NaN;
    }

    // Clean up concave regions image
    selectImage(concaveRegionsID);
    close();

    if (erosionCount >= maxErosions && verboseMode) {
        print("  Warning: Maximum erosions reached for particle " + (p + 1));
    }
}

// Re-enable screen updates
setBatchMode(false);

// Clean up working image
selectImage(workingID);
close();

// Clean up ROI manager
roiManager("reset");

// =============================================================================
// RESULTS CALCULATION AND OUTPUT
// =============================================================================

// Calculate statistics
concavitySum = 0;
validCount = 0;

for (i = 0; i < nParticles; i++) {
    if (!isNaN(concavityIndices[i])) {
        concavitySum += concavityIndices[i];
        validCount++;
    }
}

if (validCount == 0) {
    exit("Error: No valid concavity indices calculated.");
}

averageConcavityIndex = concavitySum / validCount;

// Calculate standard deviation
varianceSum = 0;
for (i = 0; i < nParticles; i++) {
    if (!isNaN(concavityIndices[i])) {
        diff = concavityIndices[i] - averageConcavityIndex;
        varianceSum += diff * diff;
    }
}
standardDeviation = sqrt(varianceSum / validCount);

// =============================================================================
// FINAL RESULTS OUTPUT
// =============================================================================

if (verboseMode) {
    print("");
    print("=== RESULTS ===");
    print("Valid particles analyzed: " + validCount + " / " + nParticles);
    print("Average largest concavity index (O2): " + d2s(averageConcavityIndex, 4));
    print("Standard deviation: " + d2s(standardDeviation, 4));
    print("Range: " + d2s(averageConcavityIndex - standardDeviation, 3) + " - " + d2s(averageConcavityIndex + standardDeviation, 3));
    print("");
    print("=== INTERPRETATION ===");
    print("O2 = 0: Perfectly convex shape (no concave regions)");
    print("O2 > 0: Higher values indicate more pronounced concave features");
    print("O2 depends on both the depth and robustness of concave regions");
}

// Store results in separate table for integration with other scripts
selectWindow(resultsTableName);

// Create particle numbers starting from 1 to match ROI numbers
particleNumbers = newArray(nParticles);
for (i = 0; i < nParticles; i++) {
    particleNumbers[i] = i + 1;  // Add 1 to match ROI numbering
}
Table.setColumn("Particle", particleNumbers);

// Create arrays for table columns (handle NaN values)
areaColumn = newArray(nParticles);
erosionColumn = newArray(nParticles);
concavityColumn = newArray(nParticles);

for (i = 0; i < nParticles; i++) {
    areaColumn[i] = particleAreas[i];
    erosionColumn[i] = erosionCounts[i];
    if (isNaN(concavityIndices[i])) {
        concavityColumn[i] = 0;  // Replace NaN with 0 for table compatibility
    } else {
        concavityColumn[i] = concavityIndices[i];
    }
}

Table.setColumn("Area_" + unit + "²", areaColumn);
Table.setColumn("Erosions_o2", erosionColumn);
Table.setColumn("Concavity_Index_O2", concavityColumn);
Table.update;

if (verboseMode) {
    print("");
    print("Analysis complete! Results stored in '" + resultsTableName + "' table.");
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

// Helper function to find minimum value in array (excluding NaN)
function getMinValue(array) {
    minVal = 1e10;
    for (i = 0; i < array.length; i++) {
        if (!isNaN(array[i]) && array[i] < minVal) {
            minVal = array[i];
        }
    }
    return minVal;
}

// Helper function to find maximum value in array (excluding NaN)
function getMaxValue(array) {
    maxVal = -1e10;
    for (i = 0; i < array.length; i++) {
        if (!isNaN(array[i]) && array[i] > maxVal) {
            maxVal = array[i];
        }
    }
    return maxVal;
}
