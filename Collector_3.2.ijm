// Comprehensive ImageJ Macro for Particle Analysis with Classification
// Calculates all particle metrics and provides interactive classification with Excel export
// Optimized for speed and integration with larger analysis workflows

// =============================================================================
// USER PARAMETERS
// =============================================================================

// Calibration settings
pixelDistance = 214;        // pixels
realDistance = 50;          // micrometers
unit = "µm";
run("Set Scale...", "distance=1 known=1 unit=pixels");

// Analysis parameters
minParticleAreaPixels = 100;    // Minimum particle area in pixels
maxErosions = 200;               // Maximum number of erosions to try
angleStepDegrees = 5;           // Angle step for Feret diameter calculation
useSubpixelRefinement = true;   // Enable subpixel boundary refinement

// Results table name
combinedTableName = "Comprehensive_Particle_Analysis";

// Classification groups
groups = newArray("Round_NonPorous", "Round_Porous", "Satellite_NonPorous",
                  "Satellite_Porous", "Splattered_NonPorous", "Splattered_Porous");

// =============================================================================
// INITIALIZATION AND VALIDATION
// =============================================================================

// Check if image is open
if (nImages == 0) {
    exit("Error: No image is open. Please open an image first.");
}

// Get original image info
originalTitle = getTitle();
originalID = getImageID();
displayID = 0;  // Will be set to the thresholded image ID later

// Calculate calibration factors
micronsPerPixel = realDistance / pixelDistance;

// Suppress verbose analysis output (as requested)
verboseMode = false;  // Always suppress verbose output during analysis

print("\\Clear");
print("=== COMPREHENSIVE PARTICLE ANALYSIS ===");
print("Image: " + originalTitle);
print("Starting analysis...");

// =============================================================================
// IMAGE PREPROCESSING
// =============================================================================

// Create working copy
selectImage(originalID);
run("Duplicate...", "title=Working");
workingID = getImageID();

// Convert to 8-bit and create binary image
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");  // Particles should be white on black background

// =============================================================================
// PARTICLE DETECTION
// =============================================================================

// =============================================================================
// DUAL MEASUREMENT STRATEGY
// =============================================================================

// STEP 1: Measure with holes included for porosity calculation
run("Set Measurements...", "area perimeter area_fraction fit shape feret's centroid redirect=None decimal=3");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude include clear add");

nParticles = nResults;
if (nParticles == 0) {
    exit("No particles found above size threshold.");
}

print("Found " + nParticles + " particles for analysis");

// Store porosity measurements (with holes included)
orig_AreaPercent = newArray(nParticles);
for (i = 0; i < nParticles; i++) {
    orig_AreaPercent[i] = getResult("%Area", i); // True porosity measurement
}

// STEP 2: Measure without holes for shape descriptors (convexity, solidity)
run("Clear Results");
run("Analyze Particles...", "size=" + minParticleAreaPixels + "-Infinity display exclude clear add");

// Store shape measurements (without holes - for surface features only)
orig_Area = newArray(nParticles);
orig_Perim = newArray(nParticles);
orig_Solidity = newArray(nParticles);
orig_Round = newArray(nParticles);
orig_Feret = newArray(nParticles);
orig_MinFeret = newArray(nParticles);
conv_perim = newArray(nParticles);

for (i = 0; i < nParticles; i++) {
    orig_Area[i] = getResult("Area", i);
    orig_Perim[i] = getResult("Perim.", i);
    orig_Solidity[i] = getResult("Solidity", i);  // Based on outline only, not holes
    orig_Round[i] = getResult("Round", i);
    orig_Feret[i] = getResult("Feret", i);
    orig_MinFeret[i] = getResult("MinFeret", i);
}

// STEP 3: Calculate convex hull perimeters correctly for convexity
run("Clear Results");
for (i = 0; i < nParticles; i++) {
    // Select the ROI for this particle
    roiManager("select", i);

    // Create convex hull of the current ROI
    run("Convex Hull");

    // Measure the convex hull perimeter
    run("Measure");
    conv_perim[i] = getResult("Perim.", i);

    // Restore original ROI
    roiManager("select", i);
    roiManager("update");
}

// Pre-calculate trigonometric values for average concavity calculation
numAngles = 180 / angleStepDegrees;
cosValues = newArray(numAngles);
sinValues = newArray(numAngles);

for (i = 0; i < numAngles; i++) {
    angleRad = i * angleStepDegrees * PI / 180;
    cosValues[i] = cos(angleRad);
    sinValues[i] = sin(angleRad);
}

// Arrays to store all analysis results
robustnessValues = newArray(nParticles);
robustnessErosions = newArray(nParticles);
largestConcavityValues = newArray(nParticles);
largestConcavityErosions = newArray(nParticles);
concavityRobustnessRatio = newArray(nParticles);
averageConcavityValues = newArray(nParticles);
particleAreas = newArray(nParticles);
solidityValues = newArray(nParticles);
convexityValues = newArray(nParticles);
roundnessValues = newArray(nParticles);
totalBlackPixelsValues = newArray(nParticles);
feretRatioValues = newArray(nParticles);

// Classification arrays
classifications = newArray(nParticles);
classified = newArray(nParticles);

// Fill classification arrays with default values
for (i = 0; i < nParticles; i++) {
    classifications[i] = "Unclassified";
    classified[i] = false;
}

// Enable batch mode for speed
setBatchMode(true);

// =============================================================================
// MAIN ANALYSIS LOOP
// =============================================================================

print("Performing comprehensive analysis...");

for (p = 0; p < nParticles; p++) {
    showProgress(p, nParticles);
    
    // Get particle measurements from results table (in pixels)
    areaPixels = getResult("Area", p);
    perimeterPixels = getResult("Perim.", p);
    areaMicrons = areaPixels * micronsPerPixel * micronsPerPixel;
    perimeterMicrons = perimeterPixels * micronsPerPixel;
    particleAreas[p] = areaMicrons;

    // Calculate additional measurements with proper logic
    solidityValues[p] = orig_Solidity[p];                    // Surface concavity only (no holes)
    convexityValues[p] = conv_perim[p] / orig_Perim[p];      // Surface convexity only (no holes)
    roundnessValues[p] = orig_Round[p];                      // Shape roundness (no holes)

    // Calculate total black pixels using porosity measurement (with holes)
    // This represents internal porosity/holes within the particle
    totalBlackPixelsValues[p] = orig_Area[p] * (100 - orig_AreaPercent[p]) / 100;

    feretRatioValues[p] = orig_Feret[p] / orig_MinFeret[p];  // Aspect ratio
    
    // =============================================================================
    // ROBUSTNESS ANALYSIS (O1)
    // =============================================================================
    
    // Create minimal working image for robustness analysis
    roiManager("select", p);
    Roi.getBounds(x, y, width, height);
    
    newImage("RobustnessWork", "8-bit black", width + 20, height + 20, 1);
    robustnessWorkID = getImageID();
    
    // Copy particle to smaller image
    selectImage(workingID);
    roiManager("select", p);
    run("Copy");
    selectImage(robustnessWorkID);
    makeRectangle(10, 10, width, height);
    run("Paste");
    run("Select None");
    
    // Count erosions for robustness
    erosionCount = 0;
    particleExists = true;
    
    getHistogram(values, counts, 256);
    if (counts[255] == 0) particleExists = false;
    
    while (particleExists && erosionCount < maxErosions) {
        run("Erode");
        erosionCount++;
        getHistogram(values, counts, 256);
        if (counts[255] == 0) particleExists = false;
    }
    
    robustnessErosions[p] = erosionCount;
    if (areaPixels > 0) {
        robustnessValues[p] = (2.0 * erosionCount) / sqrt(areaPixels);
    } else {
        robustnessValues[p] = 0;
    }
    
    selectImage(robustnessWorkID);
    close();
    
    // =============================================================================
    // AVERAGE CONCAVITY ANALYSIS
    // =============================================================================
    
    // Calculate average concavity using Feret diameter method
    roiManager("select", p);
    Roi.getCoordinates(xPixels, yPixels);
    nPoints = xPixels.length;
    
    if (nPoints >= 3) {
        // Apply subpixel refinement if enabled
        if (useSubpixelRefinement) {
            refineParticleBoundary(xPixels, yPixels);
        }
        
        // Convert coordinates to calibrated units (micrometers)
        xMicrons = newArray(nPoints);
        yMicrons = newArray(nPoints);
        for (i = 0; i < nPoints; i++) {
            xMicrons[i] = xPixels[i] * micronsPerPixel;
            yMicrons[i] = yPixels[i] * micronsPerPixel;
        }
        
        // Calculate integrated Feret diameter
        feretSum = 0;
        
        for (angleIdx = 0; angleIdx < numAngles; angleIdx++) {
            cosVal = cosValues[angleIdx];
            sinVal = sinValues[angleIdx];
            
            // Project all points onto the current direction
            minProjection = 1e10;
            maxProjection = -1e10;
            
            for (i = 0; i < nPoints; i++) {
                projection = xMicrons[i] * cosVal + yMicrons[i] * sinVal;
                if (projection < minProjection) minProjection = projection;
                if (projection > maxProjection) maxProjection = projection;
            }
            
            feretDiameter = maxProjection - minProjection;
            feretSum += feretDiameter;
        }
        
        // Calculate average Feret diameter and concavity
        avgFeretDiameter = feretSum / numAngles;
        perimeterEquivDiameterMicrons = perimeterMicrons / PI;
        averageConcavity = avgFeretDiameter / perimeterEquivDiameterMicrons;
        averageConcavityValues[p] = averageConcavity;
    } else {
        averageConcavityValues[p] = 0;
    }
    
    // =============================================================================
    // LARGEST CONCAVITY INDEX ANALYSIS (O2)
    // =============================================================================
    
    // Create working images for largest concavity analysis
    roiManager("select", p);
    Roi.getCoordinates(xCoords, yCoords);
    nPoints = xCoords.length;
    
    if (nPoints >= 3) {
        // Create minimal working image for this particle
        newImage("ConcavityWork", "8-bit black", width + 20, height + 20, 1);
        concavityWorkID = getImageID();
        
        // Translate coordinates to fit in smaller image
        for (i = 0; i < nPoints; i++) {
            xCoords[i] = xCoords[i] - x + 10;
            yCoords[i] = yCoords[i] - y + 10;
        }
        makeSelection("polygon", xCoords, yCoords);
        run("Fill", "slice");
        
        // Create convex hull version
        run("Duplicate...", "title=ConvexHull");
        convexHullID = getImageID();
        run("Convex Hull");
        run("Fill", "slice");
        
        // Calculate concave regions (difference)
        imageCalculator("Subtract create", convexHullID, concavityWorkID);
        concaveRegionsID = getImageID();
        
        // Clean up intermediate images
        selectImage(convexHullID);
        close();
        selectImage(concavityWorkID);
        close();
        
        // Count erosions on concave regions
        selectImage(concaveRegionsID);
        erosionCount = 0;
        particleExists = true;
        
        getHistogram(values, counts, 256);
        if (counts[255] == 0) particleExists = false;
        
        while (particleExists && erosionCount < maxErosions) {
            run("Erode");
            erosionCount++;
            getHistogram(values, counts, 256);
            if (counts[255] == 0) particleExists = false;
        }
        
        largestConcavityErosions[p] = erosionCount;
        if (areaPixels > 0) {
            largestConcavityValues[p] = (2.0 * erosionCount) / sqrt(areaPixels);
        } else {
            largestConcavityValues[p] = 0;
        }
        
        selectImage(concaveRegionsID);
        close();
    } else {
        largestConcavityErosions[p] = 0;
        largestConcavityValues[p] = 0;
    }
}

// Re-enable screen updates
setBatchMode(true);

// Keep the thresholded image for display and close the original
selectImage(originalID);
close();

// Rename working image to show it's the display image
selectImage(workingID);
rename("Thresholded_" + originalTitle);
displayID = getImageID();

// Create initial overlay with particle labels for easy selection
selectImage(displayID);
refreshParticleOverlay();

// =============================================================================
// RESULTS OUTPUT
// =============================================================================

// Create results table
if (isOpen(combinedTableName)) {
    selectWindow(combinedTableName);
    run("Close");
}

// Create particle numbers starting from 1 to match ROI numbers
particleNumbers = newArray(nParticles);
for (i = 0; i < nParticles; i++) {
    particleNumbers[i] = i + 1;  // Add 1 to match ROI numbering
    concavityRobustnessRatio[i] = largestConcavityValues[i] / robustnessValues[i];
}

// Create comprehensive results table with all 10 columns requested
Table.create(combinedTableName);
selectWindow(combinedTableName);
Table.setColumn("Particle", particleNumbers);
Table.setColumn("Perimeter", orig_Perim);
Table.setColumn("conv_Perimeter", conv_perim);
Table.setColumn("Area_" + unit + "²", particleAreas);
Table.setColumn("Area_Pixel" , orig_Area);
Table.setColumn("Concavity", averageConcavityValues);
Table.setColumn("Robustness_O1", robustnessValues);
Table.setColumn("Largest_Concavity_Index_O2", largestConcavityValues);
Table.setColumn("Concavity_Robustness_Ratio_O3", concavityRobustnessRatio);
Table.setColumn("Solidity", solidityValues);
Table.setColumn("Convexity", convexityValues);
Table.setColumn("Roundness", roundnessValues);
Table.setColumn("total_black_pixels", totalBlackPixelsValues);
Table.setColumn("Feret_Ratio", feretRatioValues);
Table.setColumn("Area_Percent", orig_AreaPercent);
Table.update;

// Hide the main Results table (not needed for user)
if (isOpen("Results")) {
    selectWindow("Results");
    run("Close");
}

// Calculate and display summary statistics
robustnessSum = 0;
largestConcavitySum = 0;
averageConcavitySum = 0;
validCount = 0;

for (i = 0; i < nParticles; i++) {
    robustnessSum += robustnessValues[i];
    largestConcavitySum += largestConcavityValues[i];
    averageConcavitySum += averageConcavityValues[i];
    validCount++;
}

avgRobustness = robustnessSum / validCount;
avgLargestConcavity = largestConcavitySum / validCount;
avgAverageConcavity = averageConcavitySum / validCount;

print("");
print("=== ANALYSIS COMPLETE ===");
print("Particles analyzed: " + validCount);
print("Results stored in table: " + combinedTableName);
print("All 10 measurements calculated successfully");

// Ensure the comprehensive analysis table is visible and selected
selectWindow(combinedTableName);

// Ask user if they want to manually classify particles
Dialog.create("Manual Classification");
Dialog.addMessage("Analysis complete! " + nParticles + " particles detected and analyzed.");
Dialog.addMessage("Results table contains all 10 measurements.");
Dialog.addCheckbox("Enable manual classification and Excel export", true);
Dialog.show();

if (Dialog.getCheckbox()) {
    print("");
    print("=== STARTING CLASSIFICATION ===");

    // Ensure the thresholded image with overlay is selected and visible
    selectImage(displayID);

    // Refresh the overlay to make sure particles are visible with labels
    refreshParticleOverlay();

    manualClassification(groups, classifications, classified);
    exportToExcel(groups, classifications);
} else {
    print("Classification skipped. Analysis complete!");
}

// =============================================================================
// HELPER FUNCTIONS
// =============================================================================

// Function to refine particle boundary with subpixel accuracy
function refineParticleBoundary(xCoords, yCoords) {
    nPoints = xCoords.length;
    smoothedX = newArray(nPoints);
    smoothedY = newArray(nPoints);

    // Apply Gaussian smoothing to reduce pixelation artifacts
    sigma = 0.8;  // Smoothing parameter

    for (i = 0; i < nPoints; i++) {
        weightSum = 0;
        xSum = 0;
        ySum = 0;

        // Use 5-point neighborhood for smoothing
        for (j = -2; j <= 2; j++) {
            idx = (i + j + nPoints) % nPoints;
            weight = exp(-(j * j) / (2 * sigma * sigma));
            xSum += xCoords[idx] * weight;
            ySum += yCoords[idx] * weight;
            weightSum += weight;
        }

        smoothedX[i] = xSum / weightSum;
        smoothedY[i] = ySum / weightSum;
    }

    // Copy smoothed coordinates back
    for (i = 0; i < nPoints; i++) {
        xCoords[i] = smoothedX[i];
        yCoords[i] = smoothedY[i];
    }
}

// =============================================================================
// CLASSIFICATION FUNCTIONS
// =============================================================================

// Function to refresh particle overlay with labels
function refreshParticleOverlay() {
    // Remove existing overlay
    run("Remove Overlay");

    // Recreate overlay with particle outlines and labels
    for (i = 0; i < nParticles; i++) {
        roiManager("select", i);

        // Get particle center for label placement
        Roi.getBounds(x, y, width, height);
        centerX = x + width/2;
        centerY = y + height/2;

        // Set color based on classification status
        if (classified[i]) {
            roiManager("Set Color", getGroupColor(classifications[i]));
            roiManager("Set Line Width", 3);
        } else {
            roiManager("Set Color", "yellow");
            roiManager("Set Line Width", 2);
        }

        // Add particle outline to overlay
        run("Add Selection...");

        // Add particle number label to overlay
        setColor("white");
        setFont("SansSerif", 14, "bold");
        makeText(""+(i+1), centerX-7, centerY-7);
        setColor("black");  // Black background for better visibility
        run("Add Selection...");

        // Add white text on top
        setColor("white");
        makeText(""+(i+1), centerX-7, centerY-7);
        run("Add Selection...");
    }

    // Show all ROIs
    roiManager("Show All");
    run("Select None");
}

// Function for manual classification
function manualClassification(groups, classifications, classified) {
    print("=== MANUAL CLASSIFICATION MODE ===");
    print("Instructions:");
    print("1. Click on a particle in the image (numbered overlay) or select it in ROI Manager");
    print("2. Press OK in the instruction dialog");
    print("3. Choose classification in the next dialog");
    print("4. Repeat for all particles");
    print("");
    print("Particles are numbered 1-" + nParticles + " and shown with white labels on the image");

    // Make sure ROI Manager is visible and image is selected
    if (!isOpen("ROI Manager")) {
        run("ROI Manager...");
    }

    // Ensure the thresholded display image is selected and visible
    selectImage(displayID);

    // Make sure the overlay is visible
    refreshParticleOverlay();

    // Ensure we have the correct number of ROIs
    roiCount = roiManager("count");
    if (roiCount != nParticles) {
        print("ERROR: ROI count (" + roiCount + ") doesn't match particle count (" + nParticles + ")");
        return classifications;
    }

    // Classification loop
    currentParticle = 0;
    finished = false;

    while (!finished) {
        // Show progress
        classified_count = 0;
        for (i = 0; i < nParticles; i++) {
            if (classified[i]) classified_count++;
        }

        // Ensure thresholded display image is selected and highlight current suggested particle
        selectImage(displayID);
        if (currentParticle >= 0 && currentParticle < nParticles) {
            roiManager("select", currentParticle);
        }

        // Non-blocking particle selection window
        getParticleSelection(classified_count, nParticles, currentParticle);

        // Get currently selected ROI (check if user changed selection)
        selectedIndex = roiManager("index");
        if (selectedIndex >= 0 && selectedIndex < nParticles) {
            currentParticle = selectedIndex;
        } else {
            // If no valid selection, keep current particle
            if (currentParticle < 0 || currentParticle >= nParticles) {
                currentParticle = 0;
            }
        }

        // Classification window with finalize option
        classificationResult = getClassificationAction(currentParticle, nParticles, groups);

        action = classificationResult[0];
        selectedGroup = classificationResult[1];
        shouldFinalize = classificationResult[2];

        // Handle classification first if requested
        if (action == "classify") {
            // Validate and classify
            if (currentParticle >= 0 && currentParticle < nParticles) {
                oldClassification = classifications[currentParticle];
                classifications[currentParticle] = selectedGroup;
                classified[currentParticle] = true;

                // Log classification decision
                if (oldClassification != "Unclassified" && oldClassification != selectedGroup) {
                    print("Particle " + (currentParticle + 1) + " reclassified from " + oldClassification + " to " + selectedGroup);
                } else {
                    print("Particle " + (currentParticle + 1) + " classified as " + selectedGroup);
                }

                // Refresh the overlay to show updated colors
                refreshParticleOverlay();

                // Move to next unclassified particle
                currentParticle = getNextUnclassified(classified);
            } else {
                print("ERROR: Invalid particle index: " + currentParticle);
            }
        }

        // Handle finalization after classification (if both were selected)
        if (shouldFinalize == "true") {
            finished = true;
            break;
        }

        // If action is "back", just continue the loop without classifying
    }

    print("Classification completed!");
    return classifications;
}

// Helper function to get next unclassified particle
function getNextUnclassified(classified) {
    for (i = 0; i < classified.length; i++) {
        if (!classified[i]) return i;
    }
    return 0; // All classified, return to first
}

// Non-blocking particle selection window (simplified)
function getParticleSelection(classified_count, nParticles, currentParticle) {
    // Get classification status for current particle
    currentStatus = "Unclassified";
    if (currentParticle >= 0 && currentParticle < nParticles && classified[currentParticle]) {
        currentStatus = classifications[currentParticle];
    }

    // Use waitForUser for truly non-blocking interaction
    waitForUser("Particle Selection (" + (classified_count + 1) + "/" + nParticles + ")",
               "Progress: " + classified_count + "/" + nParticles + " particles classified\n \n" +
               "INSTRUCTIONS:\n" +
               "• Click on a numbered particle in the image overlay\n" +
               "• Or select a particle in the ROI Manager\n" +
               "• Particles are numbered 1-" + nParticles + " with white labels\n \n" +
               "Currently selected: Particle " + (currentParticle + 1) + " (" + currentStatus + ")\n \n" +
               "Click OK to proceed to classification dialog\n" +
               "You can interact with the image while this dialog is open!");
}

// Classification window with classify, back, and finalize options
function getClassificationAction(currentParticle, nParticles, groups) {
    // Get current particle info
    currentStatus = "Unclassified";
    if (currentParticle >= 0 && currentParticle < nParticles && classified[currentParticle]) {
        currentStatus = classifications[currentParticle];
    }

    // Get particle area for display
    areaInfo = "";
    if (currentParticle >= 0 && currentParticle < nParticles) {
        areaInfo = "Area: " + d2s(particleAreas[currentParticle], 2) + " " + unit + "²";
    }

    Dialog.create("Classify Particle " + (currentParticle + 1));
    Dialog.addMessage("Classifying particle: " + (currentParticle + 1) + "/" + nParticles);
    Dialog.addMessage("Current status: " + currentStatus);
    Dialog.addMessage(areaInfo);
    Dialog.addChoice("Classification:", groups, groups[0]);
    Dialog.addCheckbox("Classify (save classification)", true); // Default selected
    Dialog.addCheckbox("Back (return to selection)", false);
    Dialog.addCheckbox("Finalize classification process", false);
    Dialog.show();

    selectedGroup = Dialog.getChoice();
    classify = Dialog.getCheckbox();
    back = Dialog.getCheckbox();
    finalize = Dialog.getCheckbox();

    // Determine action and finalization
    if (back) {
        return newArray("back", "", "false");
    } else if (classify && finalize) {
        // Both selected: classify first, then finalize
        return newArray("classify", selectedGroup, "true");
    } else if (classify) {
        // Only classify
        return newArray("classify", selectedGroup, "false");
    } else if (finalize) {
        // Only finalize (without classifying current particle)
        return newArray("finalize", "", "true");
    } else {
        // Nothing selected, treat as back
        return newArray("back", "", "false");
    }
}

// Helper function to assign colors to groups
function getGroupColor(group) {
    if (indexOf(group, "Round") >= 0) {
        if (indexOf(group, "Porous") >= 0) return "blue";
        else return "cyan";
    } else if (indexOf(group, "Satellite") >= 0) {
        if (indexOf(group, "Porous") >= 0) return "red";
        else return "orange";
    } else if (indexOf(group, "Splattered") >= 0) {
        if (indexOf(group, "Porous") >= 0) return "magenta";
        else return "yellow";
    }
    return "white";
}

// =============================================================================
// EXCEL EXPORT FUNCTIONS
// =============================================================================

// Function to export results to Excel
function exportToExcel(groups, classifications) {
    print("");
    print("=== EXPORTING TO EXCEL ===");

    // Get output directory
    outputDir = getDirectory("Choose directory for Excel export");
    if (outputDir == "") return;

    // Get base name from original image title (without extension)
    dotIndex = lastIndexOf(originalTitle, ".");
    if (dotIndex > 0) {
        baseName = substring(originalTitle, 0, dotIndex);
    } else {
        baseName = originalTitle;
    }

    // Create single Excel file path using original image name
    excelFile = outputDir + baseName + ".xlsx";

    // Export each group as separate worksheet in the same Excel file
    for (g = 0; g < groups.length; g++) {
        groupName = groups[g];

        // Create results table for this group
        groupIndices = newArray(0);
        for (i = 0; i < classifications.length; i++) {
            if (classifications[i] == groupName) {
                groupIndices = Array.concat(groupIndices, i);
            }
        }

        if (groupIndices.length > 0) {
            // Prepare results table for this group
            prepareGroupResults(groupIndices, groupName);

            // Export to Excel worksheet
            run("Read and Write Excel", "file=[" + excelFile + "] sheet=[" + groupName + "] no_count_column stack_results");
            print("Exported " + groupIndices.length + " particles to worksheet: " + groupName);
        } else {
            print("No particles in group: " + groupName);
        }
    }

    // Also export summary as a separate worksheet
    exportSummaryToExcel(excelFile, groups, classifications);

    // Clean up the temporary Results table used for export
    if (isOpen("Results")) {
        selectWindow("Results");
        run("Close");
    }

    print("Export completed!");
    print("Excel file saved as: " + excelFile);
    showMessage("Export completed!\nFile saved as: " + baseName + ".xlsx");
}

// Function to prepare results table for a specific group
function prepareGroupResults(indices, groupName) {
    // Clear results table
    run("Clear Results");

    // Use stored measurements and calculated values
    for (i = 0; i < indices.length; i++) {
        idx = indices[i];

        // Set all 10 measurements plus group classification
        setResult("Particle_ID", i, idx + 1);  // Use 1-based numbering for export
        setResult("Area", i, particleAreas[idx]);
        setResult("Concavity", i, averageConcavityValues[idx]);
        setResult("Robustness_O1", i, robustnessValues[idx]);
        setResult("Largest_Concavity_Index_O2", i, largestConcavityValues[idx]);
        setResult("Concavity_Robustness_Ratio_O3", i, concavityRobustnessRatio[idx]);
        setResult("Solidity", i, solidityValues[idx]);
        setResult("Convexity", i, convexityValues[idx]);
        setResult("Roundness", i, roundnessValues[idx]);
        setResult("total_black_pixels", i, totalBlackPixelsValues[idx]);
        setResult("Feret_Ratio", i, feretRatioValues[idx]);
        setResult("Group", i, groupName);
    }

    updateResults();
}

// Function to export classification summary to Excel
function exportSummaryToExcel(excelFile, groups, classifications) {
    run("Clear Results");

    for (g = 0; g < groups.length; g++) {
        count = 0;
        for (i = 0; i < classifications.length; i++) {
            if (classifications[i] == groups[g]) count++;
        }
        setResult("Group", g, groups[g]);
        setResult("Count", g, count);
        setResult("Percentage", g, (count * 100.0 / classifications.length));
    }

    updateResults();

    // Export summary to Excel worksheet
    run("Read and Write Excel", "file=[" + excelFile + "] sheet=[Summary] no_count_column stack_results");
    print("Exported summary to worksheet: Summary");
}
