// ----- Step 0: Open File Dialogs to Select Images -----
largeImagePath = File.openDialog("Select the Large Image");
smallImagePath = File.openDialog("Select the Small Image");

// Check if both images are selected
// if (largeImagePath == null || smallImagePath == null) {
//     exit("Error: Both images must be selected.");
// }

// ----- Step 1: Open the Images -----
open(largeImagePath);
run("Duplicate...", "title=Large Image");
selectWindow("Large Image");

open(smallImagePath);
run("Duplicate...", "title=Small Image");
selectWindow("Small Image");

// ----- Step 2: Template Matching -----
selectWindow("Large Image");
run("Template Matching", "template=Small Image correlation=Normalized_Correlation");

resultWindow = "Correlation Map of Small Image in Large Image";
selectWindow(resultWindow);
run("Enhance Contrast", "saturated=0.35"); // Optional for better visualization

// Optional: Apply threshold to find multiple regions of interest in the Correlation Map
// Threshold can be adjusted to control the strictness of matches
setAutoThreshold(resultWindow, "MaxEntropy dark");
run(resultWindow, "Analyze Particles...", "display add");

// ----- Step 3: Keypoint Detection using FeatureJ -----
selectWindow("Small Image");
run("FeatureJ SIFT", "compute orientation and scale"); // Detect keypoints in the small image

selectWindow("Large Image");
run("FeatureJ SIFT", "compute orientation and scale"); // Detect keypoints in the large image

// ----- Step 4: Match Keypoints and Draw Boundaries -----
run("FeatureJ Register", "source=Small Image target=Large Image method=RANSAC"); // Match points with RANSAC

// Optional: Visualize matched points and transformation
run("Draw Transformed Rectangle");
