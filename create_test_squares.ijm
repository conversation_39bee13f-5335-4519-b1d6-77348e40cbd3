// ImageJ Macro to Create Test Squares with Linearly Growing Areas
// Creates squares with areas from 300 to 100,000 pixels for robustness testing

// =============================================================================
// TEST PARAMETERS
// =============================================================================

// Square area parameters
minAreaPixels = 300;        // Smallest square area
maxAreaPixels = 100000;     // Largest square area
numSquares = 15;            // Number of squares to create (now 15 for 3 rows)

// Image settings
imageWidth = 2400;          // Image width (enlarged)
imageHeight = 1800;         // Image height (enlarged)
backgroundColor = 0;        // Black background
squareColor = 255;         // White squares

// Layout settings
marginX = 150;             // Margin from edges
marginY = 150;             // Margin from edges
minSpacing = 50;           // Minimum spacing between square edges

print("\\Clear");
print("=== CREATING TEST SQUARES FOR ROBUSTNESS VALIDATION ===");
print("Square areas: " + minAreaPixels + " to " + maxAreaPixels + " pixels");
print("Number of squares: " + numSquares + " (arranged in 3 rows of 5)");
print("Image size: " + imageWidth + " x " + imageHeight + " pixels");
print("Minimum spacing between squares: " + minSpacing + " pixels");
print("");

// =============================================================================
// CALCULATE SQUARE PARAMETERS
// =============================================================================

// Calculate areas with linear progression
areas = newArray(numSquares);
sideLengths = newArray(numSquares);
halfSides = newArray(numSquares);

areaStep = (maxAreaPixels - minAreaPixels) / (numSquares - 1);

print("--- SQUARE SPECIFICATIONS ---");
for (i = 0; i < numSquares; i++) {
    // Linear area progression
    areas[i] = minAreaPixels + i * areaStep;

    // Calculate side length from area: A = s²  =>  s = sqrt(A)
    sideLengths[i] = sqrt(areas[i]);
    halfSides[i] = sideLengths[i] / 2;

    print("Square " + (i+1) + ": Area = " + d2s(areas[i], 0) + " pixels, Side = " + d2s(sideLengths[i], 1) + " pixels");
}
print("");

// =============================================================================
// CALCULATE LAYOUT POSITIONS
// =============================================================================

// Layout - arrange squares in 3 rows of 5 squares each
maxHalfSide = halfSides[numSquares-1];
squaresPerRow = 5;
numRows = 3;

print("Layout: " + squaresPerRow + " squares per row, " + numRows + " rows");
print("Largest square half-side: " + d2s(maxHalfSide, 1) + " pixels");

// Calculate positions with proper spacing
xPositions = newArray(numSquares);
yPositions = newArray(numSquares);

// Calculate spacing to ensure squares don't overlap
availableWidth = imageWidth - 2 * marginX;
availableHeight = imageHeight - 2 * marginY;

// X spacing: account for square widths and minimum spacing
maxSideLength = sideLengths[numSquares-1];
totalSquareWidth = squaresPerRow * maxSideLength;
totalMinSpacing = (squaresPerRow - 1) * minSpacing;
requiredWidth = totalSquareWidth + totalMinSpacing;

if (requiredWidth > availableWidth) {
    print("Warning: Squares may overlap horizontally. Consider increasing image width.");
}

xSpacing = availableWidth / squaresPerRow;

// Y spacing: account for square heights and minimum spacing
totalSquareHeight = numRows * maxSideLength;
totalMinSpacingY = (numRows - 1) * minSpacing;
requiredHeight = totalSquareHeight + totalMinSpacingY;

if (requiredHeight > availableHeight) {
    print("Warning: Squares may overlap vertically. Consider increasing image height.");
}

ySpacing = availableHeight / numRows;

for (i = 0; i < numSquares; i++) {
    row = floor(i / squaresPerRow);
    col = i % squaresPerRow;

    // Calculate x position - center squares in their allocated space
    xPositions[i] = marginX + xSpacing/2 + col * xSpacing;

    // Calculate y position - center squares in their allocated space
    yPositions[i] = marginY + ySpacing/2 + row * ySpacing;

    // Debug output for spacing verification
    if (i < 3) {
        print("Square " + (i+1) + " position: (" + d2s(xPositions[i], 0) + ", " + d2s(yPositions[i], 0) + "), side: " + d2s(sideLengths[i], 1));
    }
}

// =============================================================================
// CREATE TEST IMAGE
// =============================================================================

newImage("Test_Squares_Linear_Area", "8-bit black", imageWidth, imageHeight, 1);
setForegroundColor(squareColor, squareColor, squareColor);

print("--- CREATING SQUARES ---");

for (i = 0; i < numSquares; i++) {
    x = xPositions[i];
    y = yPositions[i];
    halfSide = halfSides[i];

    // Draw filled square
    makeRectangle(x - halfSide, y - halfSide, 2 * halfSide, 2 * halfSide);
    fill();

    // Add text label above square
    setColor(squareColor);
    setFont("SansSerif", 12, "bold");
    labelText = "" + (i+1);
    drawString(labelText, x - 5, y - halfSide - 10);

    print("Created square " + (i+1) + " at (" + d2s(x, 0) + ", " + d2s(y, 0) + ")");
}

run("Select None");

// =============================================================================
// THEORETICAL ROBUSTNESS CALCULATIONS
// =============================================================================

print("");
print("--- THEORETICAL ROBUSTNESS VALUES ---");
print("For perfect squares, robustness O1 = 2*o1/sqrt(A)");
print("Theoretical o1 for square ≈ side/2 (number of erosions to disappear)");
print("Therefore, theoretical O1 ≈ 2*(side/2)/sqrt(A) = side/sqrt(A) = sqrt(A)/sqrt(A) = 1.000");
print("");

for (i = 0; i < numSquares; i++) {
    theoreticalO1 = 1.0;  // Theoretical value for perfect square
    expectedErosions = halfSides[i];     // Approximate number of erosions (half the side length)

    print("Square " + (i+1) + ": Expected ~" + d2s(expectedErosions, 0) + " erosions, Theoretical O1 ≈ " + d2s(theoreticalO1, 3));
}

// =============================================================================
// VALIDATION INFORMATION
// =============================================================================

print("");
print("=== VALIDATION INSTRUCTIONS ===");
print("1. Run particle_robustness.ijm on this test image");
print("2. Check that erosion counts approximately equal the half-side values above");
print("3. Verify that all squares give similar O1 values around 1.000");
print("4. Larger squares should take more erosions but give same O1");
print("5. If O1 values vary significantly, check the erosion detection method");
print("");

print("=== EXPECTED RESULTS ===");
print("- Erosion counts should range from ~" + d2s(halfSides[0], 0) + " to ~" + d2s(halfSides[numSquares-1], 0));
print("- All O1 values should be close to 1.000 (theoretical for squares)");
print("- Variation in O1 should be minimal (<5%) for perfect squares");
print("");

// =============================================================================
// CREATE SUMMARY TABLE
// =============================================================================

// Create summary table with expected values
if (isOpen("Square_Test_Summary")) {
    selectWindow("Square_Test_Summary");
    run("Close");
}

run("Table...", "name=Square_Test_Summary width=500 height=300");
print("[Square_Test_Summary]", "\\Headings:Square\tArea_pixels\tSide_pixels\tExpected_Erosions\tTheoretical_O1");

for (i = 0; i < numSquares; i++) {
    theoreticalO1 = 1.0;
    print("[Square_Test_Summary]", "" + (i+1) + "\t" + d2s(areas[i], 0) + "\t" + d2s(sideLengths[i], 1) + "\t" + d2s(halfSides[i], 0) + "\t" + d2s(theoreticalO1, 4));
}

print("");
print("Test squares created successfully!");
print("Image: Test_Squares_Linear_Area");
print("Summary table: Square_Test_Summary");

// =============================================================================
// ADDITIONAL TEST INFORMATION
// =============================================================================

print("");
print("=== ROBUSTNESS TESTING NOTES ===");
print("- Square robustness should be independent of size");
print("- All squares should give O1 ≈ 1.000 regardless of area");
print("- This tests the scale-invariance of the robustness measure");
print("- Deviations from 1.000 indicate issues with the calculation method");
print("");
print("=== CALIBRATION TESTING ===");
print("If using calibration (214 pixels = 50 µm):");
micronsPerPixel = 50.0 / 214.0;
print("- Smallest square: " + d2s(areas[0] * micronsPerPixel * micronsPerPixel, 1) + " µm²");
print("- Largest square: " + d2s(areas[numSquares-1] * micronsPerPixel * micronsPerPixel, 0) + " µm²");
print("- Robustness O1 should still be ~1.000 for all squares");
print("- This tests calibration independence of robustness");
