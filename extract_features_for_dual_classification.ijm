requires("1.31g");
run("8-bit");
setAutoThreshold("Minimum");
run("Make Binary");
run("Invert");
run("Set Scale...", "distance=214 known=50 unit=µm global");
run("Set Measurements...", "area perimeter area_fraction fit shape feret's");
run("Analyze Particles...", "size=100-infinity minimum=50 show=Overlay add display exclude include clear record");

n = nResults;
	
deleted_entries = 0;

for (i=n-1; i>=0; i--) {
    if ((getResult('XStart', i)>2260) && (getResult('YStart', i)>1565))	{
		roiManager("select", i);
		roiManager("Delete");
		deleted_entries++;
	}
}
n = n-deleted_entries;

conv_perim = newArray(n);

// Store original measurements to prevent issues with moved ROIs
// Create separate arrays for each measurement property
orig_Area = newArray(n);
orig_Perim = newArray(n);
orig_AreaPercent = newArray(n);
orig_Circ = newArray(n);
orig_Solidity = newArray(n);
orig_Round = newArray(n);
orig_AR = newArray(n);
orig_Major = newArray(n);
orig_Minor = newArray(n);
orig_Feret = newArray(n);
orig_FeretAngle = newArray(n);
orig_MinFeret = newArray(n);

for (i = 0; i < n; i++) {
    orig_Area[i] = getResult("Area", i);
    orig_Perim[i] = getResult("Perim.", i);
    orig_AreaPercent[i] = getResult("%Area", i);
    orig_Circ[i] = getResult("Circ.", i);
    orig_Solidity[i] = getResult("Solidity", i);
    orig_Round[i] = getResult("Round", i);
    orig_AR[i] = getResult("AR", i);
    orig_Major[i] = getResult("Major", i);
    orig_Minor[i] = getResult("Minor", i);
    orig_Feret[i] = getResult("Feret", i);
    orig_FeretAngle[i] = getResult("FeretAngle", i);
    orig_MinFeret[i] = getResult("MinFeret", i);
}

// Convexity measurements
for (i=0; i<n; i++) {
	xstart = getResult("XStart", i);
	ystart = getResult("YStart", i);
    doWand(xstart, ystart);
    run("Convex Hull");
    run("Measure");
    conv_perim[i] = getResult('Perim.', n+i);
}

run("Clear Results");

// Use stored original measurements to avoid issues with moved ROIs
for (i = 0; i < n; i++) {

    // Use original stored measurements instead of re-measuring
    setResult("Particle_ID", i, i + 1);  // Use 1-based numbering for export
    setResult("Area", i, orig_Area[i]);
    setResult("Perim.", i, orig_Perim[i]);
    setResult("%Area", i, orig_AreaPercent[i]);
    setResult("Circ.", i, orig_Circ[i]);
    setResult("Solidity", i, orig_Solidity[i]);
    setResult("Round", i, orig_Round[i]);
    setResult("AR", i, orig_AR[i]);
    //setResult("Major", i, orig_Major[i]);
    //setResult("Minor", i, orig_Minor[i]);
    //setResult("Feret", i, orig_Feret[i]);
    //setResult("FeretAngle", i, orig_FeretAngle[i]);
    //setResult("MinFeret", i, orig_MinFeret[i]);
    setResult("Convexity", i, conv_perim[i]/orig_Perim[i]);
    setResult("total_black_pixels", i, orig_Area[i]*(100-orig_AreaPercent[i]));
    setResult("FeretRatio", i, orig_Feret[i]/orig_MinFeret[i]);
}

updateResults();